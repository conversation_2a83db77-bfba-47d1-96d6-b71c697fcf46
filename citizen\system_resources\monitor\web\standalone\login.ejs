<!DOCTYPE html>
<html lang="en">

<head>
    <base href="<%= basePath %>">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>txAdmin Login</title>
    <meta name="description" content="Remotely Manage & Monitor your GTA5 FiveM Server with txAdmin v<%= txAdminVersion %> atop FXServer <%= fxServerVersion %>">
    <meta name="author" content="<PERSON>">
    <meta name="robots" content="noindex">
    <!-- Open Graph Meta Tags -->
    <meta property="og:url" content="https://github.com/tabarra/txAdmin">
    <meta property="og:type" content="website">
    <meta property="og:title" content="txAdmin - <%= serverName %>">
    <meta property="og:description" content="Remotely Manage & Monitor your GTA5 FiveM Server with txAdmin v<%= txAdminVersion %> atop FXServer <%= fxServerVersion %>">
    <meta property="og:image" content="https://i.imgur.com/Z3S9q6J.png">
    <!--style-->
    <!-- <link rel="stylesheet" href="css/simple-line-icons.css"> -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.0/css/simple-line-icons.min.css" 
        integrity="sha512-yxSQDoat2NPQ9U4mKdYra2YNenVsnRuGxVvyrirmrDKzwOdxKJVqkDEvQ17pL/PJ6B7n2n77Xm6d62bMjCBqCQ==" 
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="css/coreui.min.css" rel="stylesheet">
    <link rel="shortcut icon" type="image/png" href="img/favicon_default.png" id="favicon" />
    <link rel="stylesheet" href="css/txAdmin.css">
    <link rel="stylesheet" href="css/dark.css">

    <style>
        .cfxrebtn{
            border: solid 2px #f40552;
            background: #2e1828b2;
            box-shadow: 0 0 5px 0 rgba(244, 5, 82, 0.75);
            font-weight: 300;
            text-transform: uppercase;
            line-height: 1;
            letter-spacing: 1px;
            outline: none;
            transition: all .2s ease;
        }
        .cfxrebtn:hover{
            background-color: #f40552;
            transition: none;
        }
        .cfxrelogo svg{
            height: 20px;
            width: 100px;
            fill: #fff;
        }

        .profile-pic{
            height: 100px;
            -moz-box-shadow: 2px 4px 8px rgb(143, 143, 143);
            -webkit-box-shadow: 2px 4px 8px rgb(143, 143, 143);
            box-shadow: 2px 4px 8px rgb(143, 143, 143);
            border-radius: 50em;
        }

        .zapAdContainer{
            width: 100%;
            display: flex;
            color: unset !important;
        }
        .zapLogo {
            height: 44px;
            margin-top: auto;
            margin-bottom: auto;
        }
        .zapText {
            font-size: 1.25em;
            font-weight: 700;
            width: 100%;
            padding-left: 8px;
            align-self: center;
        }
        .zapTextIcon {
            margin-top: -0.25em;
            width: 0.9rem !important;
            height: 0.9rem !important;
            font-size: 0.9rem !important;
        }
    </style>
    <% if (isMatrix) { %>
        <style>
            * {
                font-family: Consolas, monospace, serif;
                color: lawngreen;
            }
            .c-app{
                background-color: #23282c;
            }
            pre{
                margin-bottom: -15px;
            }
            .card{
                background-color: #23282c;
                border: 4px solid lawngreen;
            }
            .alert-secondary{
                background-color: #23282c;
                border: 2px solid lawngreen;
            }
            .btn-primary {
                color: lawngreen;
                border: 2px solid lawngreen;
                background-color: transparent;
            }
            .btn-primary:hover {
                border: 2px solid black;
                color: black;
                font-weight: 700;
                background-color: lawngreen;
            }
            .hrsep::before, .hrsep::after {
                border-top: 1px dashed lawngreen;
            }
            .hrsep{
                color: lawngreen;
            }

            .card-footer {
                background-color: darkgreen;
                border-top: 2px solid lawngreen;
            }

            a {
                color: lawngreen;
                font-weight: 700;
            }

            input,
            .btn-stack-overflow,
            .btn-linkedin,
            input:-webkit-autofill,
            input:-webkit-autofill:hover,
            input:-webkit-autofill:focus,
            .input-group-text{
                background-color: transparent !important;
                border: 1px solid lawngreen !important;
                -webkit-text-fill-color: #fff;
                transition: background-color 5000s ease-in-out 0s;
            }
        </style>
    <% } %>

    <!-- injected consts -->
    <script><%- jsInjection %></script>
    
</head>

<body class="c-app flex-row align-items-center <%= uiTheme %>">
    <svg style="display:none">
        <symbol id="cfxre-icon" viewBox="51.12 12.29 1070.38 231.43">
            <g>
                <path d="M555.37060995 202.28144183c-12.72545011 2.0715849-36.40070613 4.4391105-51.49368185 4.4391105-39.95199453 0-47.35051204-19.5320862-47.35051204-75.16893786 0-57.41249584 9.17416171-75.46487856 46.46268994-75.46487856 14.50109432 0 39.06417244 2.3675256 52.38150395 4.7350512l1.1837628-26.63466302c-12.72545011-2.959407-36.99258753-7.3985175-58.00437725-7.3985175-60.66784355 0-76.05675996 31.66565492-76.05675996 104.76300788 0 69.25012386 14.2051536 104.4670672 76.05675996 104.4670672 18.94020482 0 43.79922364-3.55128841 57.70843655-6.51069541l-.8878221-27.22654443zM624.91661253 112.31546896h39.06417243V84.79298384h-39.06417243v-9.17416171c0-20.41990832 4.1431698-26.93060373 17.75644201-26.93060373 9.17416171 0 23.67525602.5918814 23.67525602.5918814l.2959407-26.04278162c0-.2959407-21.89961181-3.2553477-31.96159562-3.2553477-30.77783283 0-41.72763874 12.72545011-41.72763874 55.34091095v9.4701024H575.7904563v27.52248513h17.16456061v120.447865h31.96159563v-120.447865zM674.33894784 84.79298384l42.91140154 73.68923436-42.91140154 74.28111576h34.32912123l29.00218863-50.60585974 29.29812932 50.60585974h34.32912123l-44.09516434-75.16893786 44.09516434-72.80141226h-34.32912123l-29.29812932 51.19774114-29.00218863-51.19774114h-34.32912123zM824.38088287 190.14787312h34.62506193v42.61546084h-34.62506193zM896.5907713 232.76333396h32.25753632V129.48002957s23.08337462-10.3579245 50.01397834-15.68485711V81.53763613c-25.15495952 4.7350512-50.30991904 21.01178972-50.30991904 21.01178972V84.79298383h-31.96159563v147.97035013zM1061.42986052 207.90431514c-24.56307812 0-33.44129913-11.83762801-33.73723983-35.51288403h91.74161708l2.0715849-23.08337462c0-46.16674924-20.71584902-67.77042036-62.14754705-67.77042036-40.83981664 0-64.21913196 24.56307812-64.21913196 78.72022627 0 52.08556324 17.16456062 75.76081926 61.55566565 75.76081926 26.04278163 0 59.78002145-6.8066361 59.78002145-6.8066361l-.5918814-23.97119672s-31.07377352 2.6634663-54.45308884 2.6634663zm-34.03318053-60.37190285c.2959407-28.41030723 10.0619838-39.36011314 31.96159563-39.36011314 21.60367111 0 30.48189212 9.76604311 30.48189212 39.36011314H1027.39668z" fill-rule="nonzero">
                </path>
            </g>
            <g>
                <path d="M242.956504 146.0804v-.723216l-4.339296-57.85728c-.12113868-1.56702827-.78288132-2.92305827-1.988844-4.06809s-2.59092132-1.717638-4.158492-1.717638h-33.629544c-1.56702827 0-2.95307173.57260627-4.158492 1.717638-1.20542027 1.14503173-1.86824773 2.50106173-1.988844 4.06809l-4.339296 57.85728v.723216c-.12059627 1.446432.361608 2.65239468 1.446432 3.61608 1.084824.96368532 2.350452 1.446432 3.796884 1.446432h44.116176c1.446432 0 2.71206-.48274668 3.796884-1.446432 1.084824-.96368532 1.56757068-2.169648 1.446432-3.61608zm137.230236 84.435468c0 8.79973068-2.77172532 13.198692-8.316984 13.198692H244.58374c1.56757068 0 2.892864-.57314868 3.977688-1.717638 1.084824-1.14448932 1.56757068-2.50051932 1.446432-4.06809l-3.61608-46.285824c-.12113868-1.56757068-.78288132-2.92360068-1.988844-4.06809s-2.59092132-1.717638-4.158492-1.717638h-49.178688c-1.56702827 0-2.95307173.57314868-4.158492 1.717638-1.20542027 1.14448932-1.86824773 2.50051932-1.988844 4.06809l-3.61608 46.285824c-.12059627 1.56757068.361608 2.92360068 1.446432 4.06809s2.41065973 1.717638 3.977688 1.717638H59.440444c-5.54471627 0-8.316984-4.39896132-8.316984-13.198692 0-6.508944 1.56702827-13.50063468 4.700904-20.973264l75.395268-188.759376c.96422773-2.29024427 2.531256-4.27908827 4.700904-5.966532 2.169648-1.68744373 4.45989227-2.531256 6.870552-2.531256h61.292556c-1.56702827 0-2.95307173.57260627-4.158492 1.717638-1.20542027 1.14503173-1.86824773 2.50106173-1.988844 4.06809l-2.71206 34.714368c-.12059627 1.68744373.361608 3.073668 1.446432 4.158492 1.084824 1.084824 2.41065973 1.627236 3.977688 1.627236h30.013464c1.56757068 0 2.892864-.542412 3.977688-1.627236 1.084824-1.084824 1.56757068-2.47104827 1.446432-4.158492l-2.71206-34.714368c-.12113868-1.56702827-.78288132-2.92305827-1.988844-4.06809s-2.59092132-1.717638-4.158492-1.717638h61.292556c2.41011732 0 4.700904.84381227 6.870552 2.531256 2.169648 1.68744373 3.73721868 3.67628773 4.700904 5.966532l75.395268 188.759376c3.13333332 7.47262932 4.700904 14.46432 4.700904 20.973264z" fill-rule="nonzero">
                </path>
            </g>
        </symbol>
    </svg>
    <div class="container">

        <!-- Logo Row -->
        <% if (isWebInterface) { %>
            <% if (isMatrix) { %>
                <div class="row justify-content-center mb-4"><pre><b><%= ascii %></b></pre></div>
            <% } else { %>
                <div class="row justify-content-center mb-4">
                    <img src="<%= logoURL %>" alt="txAdmin Logo">
                </div>
            <% } %>
        <% } %>

        <!-- Card Row -->
        <div class="row justify-content-center">
            <div class="col-lg-5">
                <div class="card ">
                    <div class="card-body text-center p-4">
                        <% if (template === 'justMessage') { %>
                            <h4 class="text-danger"><%= errorTitle %></h4>
                            <p class="font-weight-bold"><%= errorMessage %></p>
                            <a href="auth" class="btn btn-primary px-4 mt-3" role="submit">Back</a>
                        <% } %>
                        <% if (template === 'noMaster') { %>
                            <h3 class="mb-4">No <code>Cfx.re</code> Account Linked</h3>
                            <div class="mx-auto">
                                <h6>Type the PIN from your terminal and click "Link Account".</h6>
                                <form action="/auth/addMaster/pin" method="post">
                                    <p class="text-success font-weight-bold mb-0 d-none" id="noMaster-autofill">Autofilled</p>
                                    <div class="input-group mx-auto" style="max-width: 140px;" id="noMaster-div">
                                        <input class="form-control form-control-lg text-center mb-3" type="text"
                                        minlength="4" maxlength="4" name="pin" placeholder="0000" 
                                        style="padding-right: 1rem !important;" autocomplete="off"  
                                        id="noMaster-pin" autofocus required>
                                    </div>
                                    <button class="btn btn-primary px-4" 
                                        type="submit">Link Account</button>
                                </form>
                            </div>
                            <script>
                                if (/^#\d{4}$/.test(window.location.hash)) {
                                    const label = document.getElementById('noMaster-autofill');
                                    const pinInput = document.getElementById('noMaster-pin');

                                    label.classList.remove('d-none');
                                    pinInput.value = window.location.hash.substring(1);
                                    pinInput.classList.add('is-valid');
                                    pinInput.onkeydown = (e) => {
                                        pinInput.classList.remove('is-valid');
                                        label.classList.add('d-none');
                                    }
                                }
                            </script>
                        <% } %>
                        <% if (template === 'callback') { %>
                            <img class="profile-pic" src="<%= addMaster_picture %>" alt="avatar">
                            <h5 class="mt-2 mb-3 text-muted"><%= addMaster_name %></h5>

                            <h5>Create <i>backup</i> password:</h5>
                            <span class="text-muted">You will be able to login using this password in case the connection with Cfx.re's auth provider fails.</span>
                            <form action="/auth/addMaster/save" method="post" class="mt-2">
                                <div class="input-group mb-2 mx-auto" style="max-width: 280px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="icon-lock"></i>
                                        </span>
                                    </div>
                                    <input class="form-control" type="password" name="password" id="password"
                                        placeholder="Password" autocomplete="new-password" minlength="6" maxlength="24" autofocus required>
                                </div>
                                <div class="input-group mx-auto" style="max-width: 280px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="icon-lock"></i>
                                        </span>
                                    </div>
                                    <input class="form-control" type="password" name="password2" id="password2"
                                        placeholder="Confirm Password" autocomplete="new-password" required>
                                </div>
                                <span class="form-text text-muted mb-4">
                                    Must be between 6 and 24 characters.
                                </span>

                                <div class="input-group mx-auto mb-2" style="max-width: 280px;">
                                    <div class="form-check">
                                        <input class="form-check-input" name="checkboxAcceptToS" id="checkboxAcceptToS" type="checkbox" required>
                                        <label class="form-check-label" for="checkboxAcceptToS">
                                            I have read and agree to the
                                            <a href="https://fivem.net/terms" target="_blank" rel="noopener noreferrer">Cfx.re ToS</a>
                                            as well as the
                                            <a href="https://github.com/tabarra/txAdmin/blob/master/LICENSE" target="_blank" rel="noopener noreferrer">txAdmin License</a>.
                                        </label>
                                    </div>
                                </div>

                                <button class="btn btn-primary px-4" id="btnCreateMaster" type="submit" disabled>Create Master Account</button>
                            </form>
                            <script>
                                const password = document.getElementById("password");
                                const password2 = document.getElementById("password2");
                                function validatePassword(){
                                    if(password.value != password2.value) {
                                        password2.setCustomValidity("Passwords Don't Match");
                                    } else {
                                        password2.setCustomValidity('');
                                    }
                                }
                                password.onchange = validatePassword;
                                password2.onkeyup = validatePassword;

                                const checkboxAcceptToS = document.getElementById("checkboxAcceptToS");
                                const btnCreateMaster = document.getElementById("btnCreateMaster");
                                checkboxAcceptToS.onchange = () => {
                                    btnCreateMaster.disabled = !checkboxAcceptToS.checked;
                                }
                            </script>
                        <% } %>
                        <% if (template === 'normal') { %>
                            <h1 class="mb-3">Login</h1>
                            <% if (isWebInterface) { %>
                                <a class="btn cfxrebtn<%= citizenfxDisabled ? ' disabled' : '' %>"
                                   role="button" href="auth/citizenfx/redirect" id="btn-cfxredirect">
                                    <i class="cfxrelogo"><svg><use href="#cfxre-icon"></use></svg></i>
                                </a>
                            <% } else { %>
                                <h5 class="text-secondary">
                                    Automatic login failed, re-open the menu or login using your username/password below.
                                </h5>
                            <% } %>
                        
                            <div class="hrsep">OR</div>

                            <form action="auth/password" id="form-password" method="post">
                                <div class="input-group mb-2 mx-auto" style="max-width: 280px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="icon-user"></i>
                                        </span>
                                    </div>
                                    <input class="form-control" type="text" name="username" id="frm-username" placeholder="Username" required>
                                </div>
                                <div class="input-group mb-4 mx-auto" style="max-width: 280px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="icon-lock"></i>
                                        </span>
                                    </div>
                                    <input class="form-control" type="password" name="password" id="frm-password" placeholder="Password" required>
                                </div>
                                <div class="row justify-content-center">
                                    <button class="btn btn-primary px-4" type="submit">Login</button>
                                </div>
                            </form>

                            <script>
                                try {
                                    const rawLocalStorageStr = localStorage.getItem('txAdminDevPassAutofill');
                                    if(rawLocalStorageStr){
                                        const [user, pass] = JSON.parse(rawLocalStorageStr);
                                        document.getElementById('frm-username').value = user;
                                        document.getElementById('frm-password').value = pass;
                                    }
                                } catch (error) {
                                    console.error('Username/Pass autofill failed', error);
                                }
                            </script>
                        <% } %>
                    </div>
                    <% if (message && template !== 'justMessage') { %>
                        <div class="card-footer text-center text-danger">
                            <strong><%= message %></strong>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>

        <!-- Info Row -->
        <div class="row justify-content-center">
            <div class="col-md-4 ">
                <div class="alert alert-secondary text-center small" style="min-width: 280px;" role="alert">
                    <h5 class="text-muted">
                        <strong>txAdmin</strong> Official Discord: <br>
                        <a href="https://discord.gg/uAmsGa2" target="_blank">
                            <img src="https://discordapp.com/api/guilds/577993482761928734/widget.png?style=shield"></img>
                        </a>
                    </h5>
                    
                    <% if (dynamicAd) { %>
                        <div class="hrsep hrsep-small">Ad</div>
                        <a class="zapAdContainer" href="<%= dynamicAd.link %>" target="_blank" rel="noopener noreferrer">
                            <img class="zapLogo show-dark-mode" alt="zap hosting" src="<%= dynamicAd.logoDarkMode %>">
                            <img class="zapLogo show-light-mode" alt="zap hosting" src="<%= dynamicAd.logoLightMode %>">
                            <div class="zapText attentionText">
                                <%- dynamicAd.text %>
                                <svg class="c-icon zapTextIcon">
                                    <use href="img/coreui_icons.svg#cil-external-link"></use>
                                </svg>
                            </div>
                        </a>
                    <% } %>

                    <div class="hrsep hrsep-small">Info</div>
                    <div class="text-muted">
                        <strong>Server profile:</strong> <%= serverProfile %> <br>
                        <strong>txAdmin version:</strong> <%= txAdminVersion %> <br>
                        <strong>FXServer build:</strong> <%= fxServerVersion %> 
                    </div>
                </div>

                <!-- Dark/Light mode toggle -->
                <% if (isWebInterface) { %>
                    <div style="text-align: center">
                        <svg class="c-sidebar-nav-icon d-none" id="darkToggleLight">
                            <use href="img/coreui_icons.svg#cil-sun"></use>
                        </svg>
                        <svg class="c-sidebar-nav-icon" id="darkToggleDark">
                            <use href="img/coreui_icons.svg#cil-moon"></use>
                        </svg>
                    </div>
                <% } %>
            </div>
        </div>
        <% if (isMatrix) { %>
            <div class="row justify-content-center">
                <div class="col-md-4 text-center pt-3">
                    <h4>The Matrix has you...</h4>
                </div>
            </div>
        <% } %>
    </div>
    
    <!-- CoreUI and necessary plugins-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" 
        integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" 
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="<%= resourcePath %>js/coreui.bundle.min.js"></script>
    <script src="<%= resourcePath %>js/txadmin/base.js"></script>
    <script>
        const searchParams = new URLSearchParams(window.location.search);
        const encodedRedirectTarget = encodeURIComponent(searchParams.get('r'));
        if(encodedRedirectTarget){
            const passwordForm = document.getElementById('form-password');
            if(passwordForm){
                passwordForm.action = `${passwordForm.action}?r=${encodedRedirectTarget}`;
            }
            const cfxRedirectButton = document.getElementById('btn-cfxredirect');
            if(cfxRedirectButton){
                cfxRedirectButton.href = `${cfxRedirectButton.href}?r=${encodedRedirectTarget}`;
            }
        }
        window.history.pushState(null, 'txAdmin', '/auth');
    </script>
</body>

</html>
