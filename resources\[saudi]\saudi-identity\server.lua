-- وظائف مساعدة لتوليد البيانات السعودية

-- توليد رقم هوية سعودي صحيح
function GenerateSaudiID()
    local id = ""
    local prefix = Config.NationalID.prefix[math.random(1, #Config.NationalID.prefix)]
    id = id .. prefix
    
    -- إضافة 9 أرقام عشوائية
    for i = 1, 9 do
        id = id .. tostring(math.random(0, 9))
    end
    
    return id
end

-- توليد تاريخ ميلاد عشوائي
function GenerateBirthDate()
    local currentYear = tonumber(os.date("%Y"))
    local minYear = currentYear - Config.BirthDateRange.maxAge
    local maxYear = currentYear - Config.BirthDateRange.minAge
    
    local year = math.random(minYear, maxYear)
    local month = math.random(1, 12)
    local day = math.random(1, 28) -- لتجنب مشاكل الأشهر المختلفة
    
    return string.format("%04d-%02d-%02d", year, month, day)
end

-- توليد اسم سعودي عشوائي
function GenerateSaudiName(gender)
    local names = Config.SaudiNames[gender] or Config.SaudiNames.male
    local firstName = names.first[math.random(1, #names.first)]
    local lastName = names.last[math.random(1, #names.last)]
    
    return firstName .. " " .. lastName
end

-- توليد مدينة ومنطقة سعودية
function GenerateSaudiLocation()
    local region = Config.SaudiRegions[math.random(1, #Config.SaudiRegions)]
    local cities = Config.SaudiCities[region]
    local city = cities and cities[math.random(1, #cities)] or region
    
    return {
        region = region,
        city = city
    }
end

-- إنشاء هوية سعودية كاملة
function CreateSaudiIdentity(playerName, gender)
    local identity = {
        nationalId = GenerateSaudiID(),
        name = playerName or GenerateSaudiName(gender or "male"),
        birthDate = GenerateBirthDate(),
        gender = gender or (math.random(1, 2) == 1 and "male" or "female"),
        nationality = "سعودي",
        location = GenerateSaudiLocation(),
        created = os.time()
    }
    
    return identity
end

-- التحقق من صحة رقم الهوية السعودية
function ValidateSaudiID(nationalId)
    if not nationalId or string.len(nationalId) ~= 10 then
        return false
    end
    
    local firstDigit = string.sub(nationalId, 1, 1)
    if firstDigit ~= "1" and firstDigit ~= "2" then
        return false
    end
    
    -- التحقق من أن جميع الأرقام صحيحة
    for i = 1, 10 do
        local digit = string.sub(nationalId, i, i)
        if not tonumber(digit) then
            return false
        end
    end
    
    return true
end

-- حدث إنشاء هوية جديدة
RegisterNetEvent('saudi-identity:createIdentity')
AddEventHandler('saudi-identity:createIdentity', function(playerName, gender)
    local src = source
    local identifier = GetPlayerIdentifier(src, 0)
    
    -- التحقق من وجود هوية مسبقة
    local existingData = exports['saudi-database']:GetPlayerData(identifier)
    if existingData and existingData.identity then
        TriggerClientEvent('saudi-identity:identityExists', src)
        return
    end
    
    -- إنشاء هوية جديدة
    local identity = CreateSaudiIdentity(playerName, gender)
    
    -- حفظ الهوية في قاعدة البيانات
    local playerData = {
        identity = identity,
        money = 5000,
        bank = 10000,
        job = "unemployed",
        job_grade = 0
    }
    
    exports['saudi-database']:SavePlayerData(identifier, playerData)
    
    -- إرسال الهوية للعميل
    TriggerClientEvent('saudi-identity:receiveIdentity', src, identity)
    
    -- تسجيل في السجلات
    exports['saudi-database']:SaveLog('identity_created', 'تم إنشاء هوية جديدة: ' .. identity.name, identifier)
    
    print("✅ تم إنشاء هوية سعودية جديدة: " .. identity.name .. " - " .. identity.nationalId)
end)

-- حدث الحصول على الهوية
RegisterNetEvent('saudi-identity:getIdentity')
AddEventHandler('saudi-identity:getIdentity', function()
    local src = source
    local identifier = GetPlayerIdentifier(src, 0)
    
    local playerData = exports['saudi-database']:GetPlayerData(identifier)
    if playerData and playerData.identity then
        TriggerClientEvent('saudi-identity:receiveIdentity', src, playerData.identity)
    else
        TriggerClientEvent('saudi-identity:noIdentity', src)
    end
end)

-- تصدير الوظائف
exports('CreateSaudiIdentity', CreateSaudiIdentity)
exports('ValidateSaudiID', ValidateSaudiID)
exports('GenerateSaudiID', GenerateSaudiID)
