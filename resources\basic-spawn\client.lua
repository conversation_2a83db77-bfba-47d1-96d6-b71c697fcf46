-- Basic spawn script
AddEventHandler('playerSpawned', function()
    print('Player spawned successfully!')
end)

-- Set spawn point
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if NetworkIsPlayerActive(PlayerId()) then
            -- Los Santos spawn point
            local spawnPos = vector3(-269.4, -957.3, 31.2)
            local spawnHeading = 205.8
            
            SetEntityCoords(PlayerPedId(), spawnPos.x, spawnPos.y, spawnPos.z, false, false, false, true)
            SetEntityHeading(PlayerPedId(), spawnHeading)
            break
        end
    end
end)
