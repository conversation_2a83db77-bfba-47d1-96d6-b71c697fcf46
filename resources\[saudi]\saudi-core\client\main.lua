-- السيرفر السعودي - العميل
-- Saudi FiveM Server - Client

print("🇸🇦 تم تحميل عميل النظام السعودي!")

-- متغيرات العميل
local playerLoaded = false

-- تهيئة اللاعب
CreateThread(function()
    while not NetworkIsPlayerActive(PlayerId()) do
        Wait(100)
    end
    
    Wait(2000) -- انتظار حتى يتم تحميل كل شيء
    
    TriggerServerEvent('saudi-core:playerReady')
    playerLoaded = true
    
    print("✅ تم تحميل اللاعب في النظام السعودي")
end)

-- رسالة ترحيب
CreateThread(function()
    while not playerLoaded do
        Wait(100)
    end
    
    Wait(3000)
    
    -- إظهار رسالة ترحيب
    SetNotificationTextEntry("STRING")
    AddTextComponentString("🇸🇦 أهلاً وسهلاً بك في السيرفر السعودي!")
    DrawNotification(false, false)
    
    -- رسالة في الدردشة
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 0},
        multiline = true,
        args = {"السيرفر السعودي", "مرحباً بك! اكتب /help للمساعدة"}
    })
end)

-- أوامر العميل
RegisterCommand('help', function()
    TriggerEvent('chat:addMessage', {
        color = {255, 255, 0},
        multiline = true,
        args = {"المساعدة", "الأوامر المتاحة:\n/help - عرض هذه الرسالة\n/me [نص] - وصف حركة\n/ooc [نص] - رسالة خارج الشخصية"}
    })
end)

RegisterCommand('me', function(source, args)
    local text = table.concat(args, " ")
    if text and text ~= "" then
        local playerName = GetPlayerName(PlayerId())
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 255},
            args = {"* " .. playerName, text}
        })
    end
end)

RegisterCommand('ooc', function(source, args)
    local text = table.concat(args, " ")
    if text and text ~= "" then
        local playerName = GetPlayerName(PlayerId())
        TriggerEvent('chat:addMessage', {
            color = {128, 128, 128},
            args = {"[OOC] " .. playerName, text}
        })
    end
end)

-- معلومات اللاعب
CreateThread(function()
    while true do
        Wait(30000) -- كل 30 ثانية
        
        if playerLoaded then
            local playerPed = PlayerPedId()
            local health = GetEntityHealth(playerPed)
            local armor = GetPedArmour(playerPed)
            
            -- يمكن إضافة المزيد من المعلومات هنا
        end
    end
end)
