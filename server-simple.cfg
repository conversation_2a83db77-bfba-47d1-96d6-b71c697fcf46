# ========================================
# إعدادات السيرفر السعودي المبسط - FiveM
# Saudi FiveM Server Simple Configuration
# ========================================

# معلومات السيرفر الأساسية
sv_hostname "🇸🇦 السيرفر السعودي | Saudi Server"
sets sv_projectName "السيرفر السعودي"
sets sv_projectDesc "سيرفر FiveM سعودي مبسط للاختبار"
sv_maxclients 32
sv_endpointprivacy true

# معلومات الاتصال
set steam_webApiKey "9DD8679C59EEBBED89D419C3F2087380"
set sv_licenseKey "cfxk_1o11FlmEpzIMLTHooysYv_46Wk7l"

# إعدادات اللغة والمنطقة الزمنية
set locale "ar_SA"
set timezone "Asia/Riyadh"

# تحميل الموارد الأساسية المطلوبة
start spawnmanager
start sessionmanager
start fivem
start hardcap
start rconlog

# موارد إضافية أساسية
start playernames
start basic-spawn

# إعدادات الأمان
set sv_scriptHookAllowed 0
set sv_enforceGameBuild 2699

# إعدادات الشبكة
endpoint_add_tcp "0.0.0.0:30121"
endpoint_add_udp "0.0.0.0:30121"

# رسالة بدء التشغيل
print("🇸🇦 تم تشغيل السيرفر السعودي المبسط بنجاح!")
print("Saudi FiveM Simple Server Started Successfully!")

# إعدادات spawn الأساسية
add_ace group.admin command allow
add_ace group.admin command.quit deny

# إعدادات إضافية لتجنب التعليق
set sv_master1 ""
set sv_master2 ""
set sv_master3 ""
set sv_master4 ""
set sv_master5 ""

# تعطيل بعض المميزات التي قد تسبب مشاكل
set sv_enableNetworkedSounds false
set sv_enableNetworkedPhones false

# إعدادات spawn
set spawnmanager_spawnPoints '[{"x": -269.4, "y": -957.3, "z": 31.2, "heading": 205.8}]'
