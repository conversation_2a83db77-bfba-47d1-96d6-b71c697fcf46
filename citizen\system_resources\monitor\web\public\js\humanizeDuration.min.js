// HumanizeDuration.js - https://git.io/j0HgmQ
//NOTE: removed all locales except en, and added shortEn
// then I minimized it https://www.digitalocean.com/community/tools/minify
!function(){var n={en:{y:function(n){return"year"+(1===n?"":"s")},mo:function(n){return"month"+(1===n?"":"s")},w:function(n){return"week"+(1===n?"":"s")},d:function(n){return"day"+(1===n?"":"s")},h:function(n){return"hour"+(1===n?"":"s")},m:function(n){return"minute"+(1===n?"":"s")},s:function(n){return"second"+(1===n?"":"s")},ms:function(n){return"millisecond"+(1===n?"":"s")},decimal:"."},shortEn:{y:()=>"y",mo:()=>"mo",w:()=>"w",d:()=>"d",h:()=>"h",m:()=>"m",s:()=>"s",ms:()=>"ms"}};function humanizer(e){var r=function humanizer(e,a){return function doHumanization(e,r){var a,u,i;e=Math.abs(e);var o,s,l,f=function getDictionary(e){var r=[e.language];if(has(e,"fallbacks")){if(!t(e.fallbacks)||!e.fallbacks.length)throw new Error("fallbacks must be an array with at least one element");r=r.concat(e.fallbacks)}for(var a=0;a<r.length;a++){var u=r[a];if(has(e.languages,u))return e.languages[u];if(has(n,u))return n[u]}throw new Error("No language found.")}(r),c=[];for(a=0,u=r.units.length;a<u;a++){if(o=r.units[a],s=r.unitMeasures[o],a+1===u)if(has(r,"maxDecimalPoints")){var m=Math.pow(10,r.maxDecimalPoints),h=e/s;l=parseFloat((Math.floor(m*h)/m).toFixed(r.maxDecimalPoints))}else l=e/s;else l=Math.floor(e/s);c.push({unitCount:l,unitName:o}),e-=l*s}var g,d,p=0;for(a=0;a<c.length;a++)if(c[a].unitCount){p=a;break}if(r.round)for(a=c.length-1;a>=0&&((i=c[a]).unitCount=Math.round(i.unitCount),0!==a);a--)d=c[a-1],g=r.unitMeasures[d.unitName]/r.unitMeasures[i.unitName],(i.unitCount%g==0||r.largest&&r.largest-1<a-p)&&(d.unitCount+=i.unitCount/g,i.unitCount=0);var v=[];for(a=0,c.length;a<u&&((i=c[a]).unitCount&&v.push(render(i.unitCount,i.unitName,f,r)),v.length!==r.largest);a++);if(!v.length)return render(0,r.units[r.units.length-1],f,r);var y;if(y=has(r,"delimiter")?r.delimiter:has(f,"delimiter")?f.delimiter:", ",!r.conjunction||1===v.length)return v.join(y);if(2===v.length)return v.join(r.conjunction);if(v.length>2)return v.slice(0,-1).join(y)+(r.serialComma?",":"")+r.conjunction+v.slice(-1)}(e,assign({},r,a||{}))};return assign(r,{language:"en",spacer:" ",conjunction:"",serialComma:!0,units:["y","mo","w","d","h","m","s"],languages:{},round:!1,unitMeasures:{y:315576e5,mo:26298e5,w:6048e5,d:864e5,h:36e5,m:6e4,s:1e3,ms:1}},e)}var e=humanizer({});function render(n,e,t,r){var a,u;a=has(r,"decimal")?r.decimal:has(t,"decimal")?t.decimal:".",u="function"==typeof t._formatCount?t._formatCount(n,a):n.toString().replace(".",a);var i,o=t[e];return i="function"==typeof o?o(n):o,t._numberFirst?i+r.spacer+u:u+r.spacer+i}function assign(n){for(var e,t=1;t<arguments.length;t++)for(var r in e=arguments[t])has(e,r)&&(n[r]=e[r]);return n}var t=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)};function has(n,e){return Object.prototype.hasOwnProperty.call(n,e)}e.getSupportedLanguages=function getSupportedLanguages(){var e=[];for(var t in n)has(n,t)&&"gr"!==t&&e.push(t);return e},e.humanizer=humanizer,"function"==typeof define&&define.amd?define((function(){return e})):"undefined"!=typeof module&&module.exports?module.exports=e:this.humanizeDuration=e}();
