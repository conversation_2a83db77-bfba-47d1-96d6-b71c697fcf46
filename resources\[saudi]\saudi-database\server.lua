local MongoDB = exports['mongodb']:getDatabase()

-- إعدادات قاعدة البيانات
local DB_NAME = GetConvar("db_name", "saudi_fivem")
local MONGODB_URL = GetConvar("mongodb_url", "mongodb://localhost:27017/saudi_fivem")

-- مجموعات قاعدة البيانات
local Collections = {
    PLAYERS = "players",
    VEHICLES = "vehicles", 
    JOBS = "jobs",
    INVENTORY = "inventory",
    HOUSES = "houses",
    GANGS = "gangs",
    LOGS = "logs"
}

-- تهيئة قاعدة البيانات
CreateThread(function()
    print("🇸🇦 جاري تهيئة قاعدة البيانات MongoDB...")
    
    -- إنشاء الفهارس
    MongoDB.createIndex(Collections.PLAYERS, {identifier = 1}, {unique = true})
    MongoDB.createIndex(Collections.VEHICLES, {owner = 1})
    MongoDB.createIndex(Collections.INVENTORY, {owner = 1})
    
    print("✅ تم تهيئة قاعدة البيانات بنجاح!")
end)

-- وظائف اللاعبين
function GetPlayerData(identifier)
    return MongoDB.findOne(Collections.PLAYERS, {identifier = identifier})
end

function SavePlayerData(identifier, data)
    return MongoDB.updateOne(
        Collections.PLAYERS,
        {identifier = identifier},
        {["$set"] = data},
        {upsert = true}
    )
end

function CreateNewPlayer(identifier, name)
    local playerData = {
        identifier = identifier,
        name = name,
        money = 5000,
        bank = 10000,
        job = "unemployed",
        job_grade = 0,
        position = {x = -1037.0, y = -2737.0, z = 20.0},
        inventory = {},
        metadata = {
            hunger = 100,
            thirst = 100,
            stress = 0,
            health = 200,
            armor = 0
        },
        created_at = os.time(),
        last_login = os.time()
    }
    
    return MongoDB.insertOne(Collections.PLAYERS, playerData)
end

-- وظائف المركبات
function GetPlayerVehicles(identifier)
    return MongoDB.find(Collections.VEHICLES, {owner = identifier})
end

function SaveVehicle(vehicleData)
    return MongoDB.insertOne(Collections.VEHICLES, vehicleData)
end

function UpdateVehicle(plate, data)
    return MongoDB.updateOne(
        Collections.VEHICLES,
        {plate = plate},
        {["$set"] = data}
    )
end

-- وظائف السجلات
function SaveLog(logType, message, playerIdentifier)
    local logData = {
        type = logType,
        message = message,
        player = playerIdentifier,
        timestamp = os.time(),
        date = os.date("%Y-%m-%d %H:%M:%S")
    }
    
    return MongoDB.insertOne(Collections.LOGS, logData)
end

-- تصدير الوظائف
exports('GetPlayerData', GetPlayerData)
exports('SavePlayerData', SavePlayerData)
exports('CreateNewPlayer', CreateNewPlayer)
exports('GetPlayerVehicles', GetPlayerVehicles)
exports('SaveVehicle', SaveVehicle)
exports('UpdateVehicle', UpdateVehicle)
exports('SaveLog', SaveLog)

-- أحداث الشبكة
RegisterNetEvent('saudi-database:getPlayerData')
AddEventHandler('saudi-database:getPlayerData', function()
    local src = source
    local identifier = GetPlayerIdentifier(src, 0)
    local playerData = GetPlayerData(identifier)
    
    TriggerClientEvent('saudi-database:receivePlayerData', src, playerData)
end)

RegisterNetEvent('saudi-database:savePlayerData')
AddEventHandler('saudi-database:savePlayerData', function(data)
    local src = source
    local identifier = GetPlayerIdentifier(src, 0)
    SavePlayerData(identifier, data)
end)
