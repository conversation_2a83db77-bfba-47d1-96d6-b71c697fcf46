const { MongoClient } = require('mongodb');

// إعدادات قاعدة البيانات
const DB_URL = 'mongodb://localhost:27017';
const DB_NAME = 'saudi_fivem';

// مجموعات قاعدة البيانات
const collections = [
    'players',
    'vehicles', 
    'jobs',
    'inventory',
    'houses',
    'gangs',
    'logs',
    'economy',
    'chat_logs'
];

// الفهارس المطلوبة
const indexes = {
    players: [
        { key: { identifier: 1 }, options: { unique: true } },
        { key: { nationalId: 1 }, options: { unique: true } },
        { key: { name: 1 } }
    ],
    vehicles: [
        { key: { owner: 1 } },
        { key: { plate: 1 }, options: { unique: true } }
    ],
    inventory: [
        { key: { owner: 1 } }
    ],
    logs: [
        { key: { timestamp: -1 } },
        { key: { player: 1 } },
        { key: { type: 1 } }
    ],
    chat_logs: [
        { key: { timestamp: -1 } },
        { key: { player: 1 } }
    ]
};

// البيانات الأولية
const initialData = {
    jobs: [
        {
            name: 'unemployed',
            label: 'عاطل عن العمل',
            defaultDuty: false,
            offDutyPay: false,
            grades: [
                { grade: 0, name: 'عاطل', payment: 0 }
            ]
        },
        {
            name: 'police',
            label: 'الشرطة السعودية',
            defaultDuty: true,
            offDutyPay: false,
            grades: [
                { grade: 0, name: 'جندي', payment: 3500 },
                { grade: 1, name: 'جندي أول', payment: 4000 },
                { grade: 2, name: 'عريف', payment: 4500 },
                { grade: 3, name: 'وكيل رقيب', payment: 5000 },
                { grade: 4, name: 'رقيب', payment: 5500 },
                { grade: 5, name: 'رقيب أول', payment: 6000 },
                { grade: 6, name: 'ملازم', payment: 7000 },
                { grade: 7, name: 'ملازم أول', payment: 8000 },
                { grade: 8, name: 'نقيب', payment: 9000 },
                { grade: 9, name: 'رائد', payment: 10000 },
                { grade: 10, name: 'مقدم', payment: 12000 },
                { grade: 11, name: 'عقيد', payment: 15000 },
                { grade: 12, name: 'عميد', payment: 18000 }
            ]
        },
        {
            name: 'ambulance',
            label: 'الهلال الأحمر السعودي',
            defaultDuty: true,
            offDutyPay: false,
            grades: [
                { grade: 0, name: 'مسعف متدرب', payment: 3000 },
                { grade: 1, name: 'مسعف', payment: 4000 },
                { grade: 2, name: 'مسعف أول', payment: 5000 },
                { grade: 3, name: 'فني طوارئ طبية', payment: 6000 },
                { grade: 4, name: 'أخصائي طوارئ', payment: 7500 },
                { grade: 5, name: 'مشرف طوارئ', payment: 9000 },
                { grade: 6, name: 'طبيب طوارئ', payment: 12000 },
                { grade: 7, name: 'استشاري طوارئ', payment: 15000 }
            ]
        }
    ]
};

async function setupDatabase() {
    let client;
    
    try {
        console.log('🇸🇦 بدء إعداد قاعدة البيانات السعودية...');
        
        // الاتصال بقاعدة البيانات
        client = new MongoClient(DB_URL);
        await client.connect();
        console.log('✅ تم الاتصال بـ MongoDB بنجاح');
        
        const db = client.db(DB_NAME);
        
        // إنشاء المجموعات
        console.log('📁 إنشاء المجموعات...');
        for (const collectionName of collections) {
            try {
                await db.createCollection(collectionName);
                console.log(`✅ تم إنشاء مجموعة: ${collectionName}`);
            } catch (error) {
                if (error.code === 48) {
                    console.log(`ℹ️  المجموعة موجودة مسبقاً: ${collectionName}`);
                } else {
                    throw error;
                }
            }
        }
        
        // إنشاء الفهارس
        console.log('🔍 إنشاء الفهارس...');
        for (const [collectionName, collectionIndexes] of Object.entries(indexes)) {
            const collection = db.collection(collectionName);
            for (const index of collectionIndexes) {
                try {
                    await collection.createIndex(index.key, index.options || {});
                    console.log(`✅ تم إنشاء فهرس في ${collectionName}: ${JSON.stringify(index.key)}`);
                } catch (error) {
                    console.log(`⚠️  فهرس موجود مسبقاً في ${collectionName}: ${JSON.stringify(index.key)}`);
                }
            }
        }
        
        // إدراج البيانات الأولية
        console.log('📊 إدراج البيانات الأولية...');
        for (const [collectionName, data] of Object.entries(initialData)) {
            const collection = db.collection(collectionName);
            
            // التحقق من وجود البيانات
            const existingCount = await collection.countDocuments();
            if (existingCount === 0) {
                await collection.insertMany(data);
                console.log(`✅ تم إدراج البيانات الأولية في: ${collectionName}`);
            } else {
                console.log(`ℹ️  البيانات موجودة مسبقاً في: ${collectionName}`);
            }
        }
        
        // إنشاء مستخدم إداري افتراضي (اختياري)
        const playersCollection = db.collection('players');
        const adminExists = await playersCollection.findOne({ identifier: 'steam:admin' });
        
        if (!adminExists) {
            const adminPlayer = {
                identifier: 'steam:admin',
                name: 'المدير العام',
                nationalId: '**********',
                money: 1000000,
                bank: 5000000,
                job: 'government',
                job_grade: 4,
                position: { x: -1037.0, y: -2737.0, z: 20.0 },
                inventory: [],
                metadata: {
                    hunger: 100,
                    thirst: 100,
                    stress: 0,
                    health: 200,
                    armor: 0
                },
                identity: {
                    nationalId: '**********',
                    name: 'المدير العام',
                    birthDate: '1990-01-01',
                    gender: 'male',
                    nationality: 'سعودي',
                    location: {
                        region: 'الرياض',
                        city: 'الرياض'
                    }
                },
                created_at: new Date(),
                last_login: new Date()
            };
            
            await playersCollection.insertOne(adminPlayer);
            console.log('✅ تم إنشاء حساب المدير الافتراضي');
        }
        
        console.log('🎉 تم إعداد قاعدة البيانات السعودية بنجاح!');
        console.log('📋 ملخص:');
        console.log(`   - قاعدة البيانات: ${DB_NAME}`);
        console.log(`   - المجموعات: ${collections.length}`);
        console.log(`   - الفهارس: ${Object.values(indexes).flat().length}`);
        console.log('');
        console.log('🚀 يمكنك الآن تشغيل سيرفر FiveM!');
        
    } catch (error) {
        console.error('❌ خطأ في إعداد قاعدة البيانات:', error);
        process.exit(1);
    } finally {
        if (client) {
            await client.close();
        }
    }
}

// تشغيل الإعداد
setupDatabase();
