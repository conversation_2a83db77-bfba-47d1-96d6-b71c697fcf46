-- السيرفر السعودي - النظام الأساسي
-- Saudi FiveM Server - Core System

print("🇸🇦 تم تحميل النظام الأساسي السعودي!")
print("Saudi Core System Loaded!")

-- متغيرات عامة
SaudiCore = {}
SaudiCore.Players = {}

-- وظائف مساعدة
function SaudiCore.GetPlayer(source)
    return SaudiCore.Players[source]
end

function SaudiCore.CreatePlayer(source, identifier)
    local player = {
        source = source,
        identifier = identifier,
        name = GetPlayerName(source),
        money = 5000,
        bank = 10000,
        job = "unemployed",
        job_grade = 0
    }
    
    SaudiCore.Players[source] = player
    return player
end

-- أحداث اللاعبين
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    deferrals.defer()
    deferrals.update("🇸🇦 مرحباً بك في السيرفر السعودي...")
    
    Wait(1000)
    
    deferrals.update("جاري التحقق من البيانات...")
    Wait(1000)
    
    deferrals.done()
    
    print("🇸🇦 لاعب جديد: " .. name .. " (" .. identifier .. ")")
end)

AddEventHandler('playerDropped', function(reason)
    local source = source
    SaudiCore.Players[source] = nil
    print("👋 لاعب غادر السيرفر: " .. GetPlayerName(source))
end)

-- رسالة ترحيب
RegisterNetEvent('saudi-core:playerReady')
AddEventHandler('saudi-core:playerReady', function()
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    SaudiCore.CreatePlayer(source, identifier)
    
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        multiline = true,
        args = {"السيرفر السعودي", "🇸🇦 أهلاً وسهلاً بك في السيرفر السعودي!"}
    })
end)

-- أوامر إدارية
RegisterCommand('money', function(source, args, rawCommand)
    if source == 0 then -- Console
        if args[1] and args[2] then
            local targetId = tonumber(args[1])
            local amount = tonumber(args[2])
            
            if SaudiCore.Players[targetId] then
                SaudiCore.Players[targetId].money = amount
                TriggerClientEvent('chat:addMessage', targetId, {
                    color = {0, 255, 0},
                    args = {"النظام", "تم تحديث أموالك إلى: " .. amount .. " ر.س"}
                })
                print("تم تحديث أموال اللاعب " .. targetId .. " إلى: " .. amount)
            end
        end
    end
end, true)

-- تصدير الوظائف
exports('GetPlayer', SaudiCore.GetPlayer)
exports('CreatePlayer', SaudiCore.CreatePlayer)
