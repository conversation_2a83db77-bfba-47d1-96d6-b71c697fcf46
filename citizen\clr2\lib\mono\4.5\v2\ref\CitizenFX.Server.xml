<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CitizenFX.Server</name>
    </assembly>
    <members>
        <member name="M:CitizenFX.Server.Native.Natives.AddBlipForArea(System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Adds a rectangular blip for the specified coordinates/area.
            
            It is recommended to use [SET_BLIP_ROTATION](#\_0xF87683CDF73C3F6E) and [SET_BLIP_COLOUR](#\_0x03D7FB09E75D6B7E) to make the blip not rotate along with the camera.
            
            By default, the blip will show as a *regular* blip with the specified color/sprite if it is outside of the minimap view.
            
            Example image:
            ![minimap](https://i.imgur.com/qLbXWcQ.png)
            ![big map](https://i.imgur.com/0j7O7Rh.png)
            
            (Native name is *likely* to actually be ADD_BLIP_FOR_AREA, but due to the usual reasons this can't be confirmed)
            </summary>
            <param name="x">
            The X coordinate of the center of the blip.
            </param>
            <param name="y">
            The Y coordinate of the center of the blip.
            </param>
            <param name="z">
            The Z coordinate of the center of the blip.
            </param>
            <param name="width">
            The width of the blip.
            </param>
            <param name="height">
            The height of the blip.
            </param>
            <returns>
            A handle to the blip.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddBlipForCoord(System.Single,System.Single,System.Single)">
            <summary>
            Creates a blip for the specified coordinates. You can use `SET_BLIP_` natives to change the blip.
            </summary>
            <param name="x">
            The X coordinate to create the blip on.
            </param>
            <param name="y">
            The Y coordinate.
            </param>
            <param name="z">
            The Z coordinate.
            </param>
            <returns>
            A blip handle.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddBlipForEntity(System.Int32)">
            <summary>
            Create a blip that by default is red (enemy), you can use [SET_BLIP_AS_FRIENDLY](#\_0xC6F43D0E) to make it blue (friend).\
            Can be used for objects, vehicles and peds.
            
            Example of enemy:
            ![enemy](https://i.imgur.com/fl78svv.png)
            Example of friend:
            ![friend](https://i.imgur.com/Q16ho5d.png)
            </summary>
            <param name="entity">
            The entity handle to create the blip.
            </param>
            <returns>
            A blip handle.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddBlipForRadius(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Create a blip with a radius for the specified coordinates (it doesnt create the blip sprite, so you need to use [AddBlipCoords](#\_0xC6F43D0E))
            
            Example image:
            ![example](https://i.imgur.com/9hQl3DB.png)
            </summary>
            <param name="posX">
            The x position of the blip (you can also send a vector3 instead of the bulk coordinates)
            </param>
            <param name="posY">
            The y position of the blip (you can also send a vector3 instead of the bulk coordinates)
            </param>
            <param name="posZ">
            The z position of the blip (you can also send a vector3 instead of the bulk coordinates)
            </param>
            <param name="radius">
            The number that defines the radius of the blip circle
            </param>
            <returns>
            The blip handle that can be manipulated with every `SetBlip` natives
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddPedDecorationFromHashes(System.Int32,System.UInt32,System.UInt32)">
            <summary>
            Applies an Item from a PedDecorationCollection to a ped. These include tattoos and shirt decals.
            collection - PedDecorationCollection filename hash
            overlay - Item name hash
            Example:
            Entry inside "mpbeach_overlays.xml" -
            &lt;Item&gt;
              &lt;uvPos x="0.500000" y="0.500000" /&gt;
              &lt;scale x="0.600000" y="0.500000" /&gt;
              &lt;rotation value="0.000000" /&gt;
              &lt;nameHash&gt;FM_Hair_Fuzz&lt;/nameHash&gt;
              &lt;txdHash&gt;mp_hair_fuzz&lt;/txdHash&gt;
              &lt;txtHash&gt;mp_hair_fuzz&lt;/txtHash&gt;
              &lt;zone&gt;ZONE_HEAD&lt;/zone&gt;
              &lt;type&gt;TYPE_TATTOO&lt;/type&gt;
              &lt;faction&gt;FM&lt;/faction&gt;
              &lt;garment&gt;All&lt;/garment&gt;
              &lt;gender&gt;GENDER_DONTCARE&lt;/gender&gt;
              &lt;award /&gt;
              &lt;awardLevel /&gt;
            &lt;/Item&gt;
            Code:
            PED::_0x5F5D1665E352A839(PLAYER::PLAYER_PED_ID(), MISC::GET_HASH_KEY("mpbeach_overlays"), MISC::GET_HASH_KEY("fm_hair_fuzz"))
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.AddStateBagChangeHandler(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Delegate)">
            <summary>
            Adds a handler for changes to a state bag.
            
            The function called expects to match the following signature:
            
            ```ts
            function StateBagChangeHandler(bagName: string, key: string, value: any, reserved: number, replicated: boolean);
            ```
            
            *   **bagName**: The internal bag ID for the state bag which changed. This is usually `player:Source`, `entity:NetID`
                or `localEntity:Handle`.
            *   **key**: The changed key.
            *   **value**: The new value stored at key. The old value is still stored in the state bag at the time this callback executes.
            *   **reserved**: Currently unused.
            *   **replicated**: Whether the set is meant to be replicated.
            
            At this time, the change handler can't opt to reject changes.
            
            If bagName refers to an entity, use [GET_ENTITY_FROM_STATE_BAG_NAME](?\_0x4BDF1868) to get the entity handle
            If bagName refers to a player, use [GET_PLAYER_FROM_STATE_BAG_NAME](?\_0xA56135E0) to get the player handle
            </summary>
            <param name="keyFilter">
            The key to check for, or null for no filter.
            </param>
            <param name="bagFilter">
            The bag ID to check for such as `entity:65535`, or null for no filter.
            </param>
            <param name="handler">
            The handler function.
            </param>
            <returns>
            A cookie to remove the change handler.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ApplyForceToEntity(System.Int32,System.Int32,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Int32,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Applies a force to the specified entity.
            
            **List of force types (p1)**:
            
            ```
            public enum ForceType
            {
                MinForce = 0,
                MaxForceRot = 1,
                MinForce2 = 2,
                MaxForceRot2 = 3,
                ForceNoRot = 4,
                ForceRotPlusForce = 5
            }
            ```
            
            Research/documentation on the gtaforums can be found [here](https://gtaforums.com/topic/885669-precisely-define-object-physics/) and [here](https://gtaforums.com/topic/887362-apply-forces-and-momentums-to-entityobject/).
            </summary>
            <param name="entity">
            The entity you want to apply a force on
            </param>
            <param name="forceType">
            See native description above for a list of commonly used values
            </param>
            <param name="x">
            Force amount (X)
            </param>
            <param name="y">
            Force amount (Y)
            </param>
            <param name="z">
            Force amount (Z)
            </param>
            <param name="offX">
            Rotation/offset force (X)
            </param>
            <param name="offY">
            Rotation/offset force (Y)
            </param>
            <param name="offZ">
            Rotation/offset force (Z)
            </param>
            <param name="boneIndex">
            (Often 0) Entity bone index
            </param>
            <param name="isDirectionRel">
            (Usually false) Vector defined in local (body-fixed) coordinate frame
            </param>
            <param name="ignoreUpVec">
            (Usually true)
            </param>
            <param name="isForceRel">
            (Usually true) When true, force gets multiplied with the objects mass and different objects will have the same acceleration
            </param>
            <param name="p12">
            (Usually false)
            </param>
            <param name="p13">
            (Usually true)
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CanPlayerStartCommerceSession(CitizenFX.Core.CString)">
            <summary>
            Returns whether or not the specified player has enough information to start a commerce session for.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <returns>
            True or false.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CancelEvent">
            <summary>
            Cancels the currently executing event.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ClearPedProp(System.Int32,System.Int32)">
            <summary>
            CLEAR_PED_PROP
            </summary>
            <param name="ped">
            The ped handle.
            </param>
            <param name="propId">
            The prop id you want to clear from the ped. Refer to [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F).
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ClearPedSecondaryTask(System.Int32)">
            <summary>
            CLEAR_PED_SECONDARY_TASK
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ClearPedTasks(System.Int32)">
            <summary>
            Clear a ped's tasks. Stop animations and other tasks created by scripts.
            </summary>
            <param name="ped">
            Ped id. Use PlayerPedId() for your own character.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ClearPedTasksImmediately(System.Int32)">
            <summary>
            Immediately stops the pedestrian from whatever it's doing. The difference between this and [CLEAR_PED_TASKS](#\_0xE1EF3C1216AFF2CD) is that this one teleports the ped but does not change the position of the ped.
            </summary>
            <param name="ped">
            Ped id.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ClearPlayerWantedLevel(CitizenFX.Core.CString)">
            <summary>
            This executes at the same as speed as PLAYER::SET_PLAYER_WANTED_LEVEL(player, 0, false);  
            PLAYER::GET_PLAYER_WANTED_LEVEL(player); executes in less than half the time. Which means that it's worth first checking if the wanted level needs to be cleared before clearing. However, this is mostly about good code practice and can important in other situations. The difference in time in this example is negligible.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreateObject(System.UInt32,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates an object (prop) with the specified model at the specified position, offset on the Z axis by the radius of the object's model.
            This object will initially be owned by the creating script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            </summary>
            <param name="modelHash">
            The model to spawn.
            </param>
            <param name="x">
            Spawn coordinate X component.
            </param>
            <param name="y">
            Spawn coordinate Y component.
            </param>
            <param name="z">
            Spawn coordinate Z component, 'ground level'.
            </param>
            <param name="isNetwork">
            Whether to create a network object for the object. If false, the object exists only locally.
            </param>
            <param name="netMissionEntity">
            Whether to register the object as pinned to the script host in the R\* network model.
            </param>
            <param name="doorFlag">
            False to create a door archetype (archetype flag bit 26 set) as a door. Required to be set to true to create door models in network mode.
            </param>
            <returns>
            A script handle (fwScriptGuid index) for the object, or `0` if the object failed to be created.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreateObjectNoOffset(System.UInt32,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates an object (prop) with the specified model centered at the specified position.
            This object will initially be owned by the creating script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            </summary>
            <param name="modelHash">
            The model to spawn.
            </param>
            <param name="x">
            Spawn coordinate X component.
            </param>
            <param name="y">
            Spawn coordinate Y component.
            </param>
            <param name="z">
            Spawn coordinate Z component.
            </param>
            <param name="isNetwork">
            Whether to create a network object for the object. If false, the object exists only locally.
            </param>
            <param name="netMissionEntity">
            Whether to register the object as pinned to the script host in the R\* network model.
            </param>
            <param name="doorFlag">
            False to create a door archetype (archetype flag bit 26 set) as a door. Required to be set to true to create door models in network mode.
            </param>
            <returns>
            A script handle (fwScriptGuid index) for the object, or `0` if the object failed to be created.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreatePed(System.Int32,System.UInt32,System.Single,System.Single,System.Single,System.Single,System.Boolean,System.Boolean)">
            <summary>
            Creates a ped (biped character, pedestrian, actor) with the specified model at the specified position and heading.
            This ped will initially be owned by the creating script as a mission entity, and the model should be loaded already
            (e.g. using REQUEST_MODEL).
            </summary>
            <param name="pedType">
            Unused. Peds get set to CIVMALE/CIVFEMALE/etc. no matter the value specified.
            </param>
            <param name="modelHash">
            The model of ped to spawn.
            </param>
            <param name="x">
            Spawn coordinate X component.
            </param>
            <param name="y">
            Spawn coordinate Y component.
            </param>
            <param name="z">
            Spawn coordinate Z component.
            </param>
            <param name="heading">
            Heading to face towards, in degrees.
            </param>
            <param name="isNetwork">
            Whether to create a network object for the ped. If false, the ped exists only locally.
            </param>
            <param name="bScriptHostPed">
            Whether to register the ped as pinned to the script host in the R\* network model.
            </param>
            <returns>
            A script handle (fwScriptGuid index) for the ped, or `0` if the ped failed to be created.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreatePedInsideVehicle(System.Int32,System.Int32,System.UInt32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            CREATE_PED_INSIDE_VEHICLE
            </summary>
            <param name="pedType">
            See [`CREATE_PED`](#\_0xD49F9B0955C367DE)
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreateVehicle(System.UInt32,System.Single,System.Single,System.Single,System.Single,System.Boolean,System.Boolean)">
            <summary>
            Creates a vehicle with the specified model at the specified position. This vehicle will initially be owned by the creating
            script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            
            ```
            NativeDB Added Parameter 8: BOOL p7
            ```
            </summary>
            <param name="modelHash">
            The model of vehicle to spawn.
            </param>
            <param name="x">
            Spawn coordinate X component.
            </param>
            <param name="y">
            Spawn coordinate Y component.
            </param>
            <param name="z">
            Spawn coordinate Z component.
            </param>
            <param name="heading">
            Heading to face towards, in degrees.
            </param>
            <param name="isNetwork">
            Whether to create a network object for the vehicle. If false, the vehicle exists only locally.
            </param>
            <param name="netMissionEntity">
            Whether to register the vehicle as pinned to the script host in the R\* network model.
            </param>
            <returns>
            A script handle (fwScriptGuid index) for the vehicle, or `0` if the vehicle failed to be created.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.CreateVehicleServerSetter(System.UInt32,CitizenFX.Core.CString,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Equivalent to CREATE_VEHICLE, but it uses 'server setter' logic (like the former CREATE_AUTOMOBILE) as a workaround for
            reliability concerns regarding entity creation RPC.
            
            Unlike CREATE_AUTOMOBILE, this supports other vehicle types as well.
            </summary>
            <param name="modelHash">
            The model of vehicle to spawn.
            </param>
            <param name="type">
            The appropriate vehicle type for the model info. Can be one of `automobile`, `bike`, `boat`, `heli`, `plane`, `submarine`, `trailer`, and (potentially), `train`. This should be the same type as the `type` field in `vehicles.meta`.
            </param>
            <param name="x">
            Spawn coordinate X component.
            </param>
            <param name="y">
            Spawn coordinate Y component.
            </param>
            <param name="z">
            Spawn coordinate Z component.
            </param>
            <param name="heading">
            Heading to face towards, in degrees.
            </param>
            <returns>
            A script handle for the vehicle.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DeleteEntity(System.Int32)">
            <summary>
            Deletes the specified entity.
            </summary>
            <param name="entity">
            The entity to delete.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DeleteFunctionReference(CitizenFX.Core.CString)">
            <summary>
            DELETE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DeleteResourceKvp(CitizenFX.Core.CString)">
            <summary>
            DELETE_RESOURCE_KVP
            </summary>
            <param name="key">
            The key to delete
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DeleteResourceKvpNoSync(CitizenFX.Core.CString)">
            <summary>
            Nonsynchronous [DELETE_RESOURCE_KVP](#\_0x7389B5DF) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to delete
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesBoatSinkWhenWrecked(System.Int32)">
            <summary>
            DOES_BOAT_SINK_WHEN_WRECKED
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            Returns whether or not the boat sinks when wrecked.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesEntityExist(System.Int32)">
            <summary>
            DOES_ENTITY_EXIST
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesPlayerExist(CitizenFX.Core.CString)">
            <summary>
            Returns whether or not the player exists
            </summary>
            <returns>
            True if the player exists, false otherwise
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesPlayerOwnSku(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Requests whether or not the player owns the specified SKU.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <param name="skuId">
            The ID of the SKU.
            </param>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DoesPlayerOwnSkuExt(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Requests whether or not the player owns the specified package.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <param name="skuId">
            The package ID on Tebex.
            </param>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DropPlayer(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            DROP_PLAYER
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.DuplicateFunctionReference(CitizenFX.Core.CString)">
            <summary>
            DUPLICATE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.EnableEnhancedHostSupport(System.Boolean)">
            <summary>
            ENABLE_ENHANCED_HOST_SUPPORT
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.EndFindKvp(System.Int32)">
            <summary>
            END_FIND_KVP
            </summary>
            <param name="handle">
            The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)
            </param>
            <returns>
            None.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.EnsureEntityStateBag(System.Int32)">
            <summary>
            Internal function for ensuring an entity has a state bag.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ExecuteCommand(CitizenFX.Core.CString)">
            <summary>
            EXECUTE_COMMAND
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.FindKvp(System.Int32)">
            <summary>
            FIND_KVP
            </summary>
            <param name="handle">
            The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)
            </param>
            <returns>
            None.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.FlagServerAsPrivate(System.Boolean)">
            <summary>
            FLAG_SERVER_AS_PRIVATE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.FlushResourceKvp">
            <summary>
            Nonsynchronous operations will not wait for a disk/filesystem flush before returning from a write or delete call. They will be much faster than their synchronous counterparts (e.g., bulk operations), however, a system crash may lose the data to some recent operations.
            
            This native ensures all `_NO_SYNC` operations are synchronized with the disk/filesystem.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.FreezeEntityPosition(System.Int32,System.Boolean)">
            <summary>
            Freezes or unfreezes an entity preventing its coordinates to change by the player if set to `true`. You can still change the entity position using SET_ENTITY_COORDS.
            </summary>
            <param name="entity">
            The entity to freeze/unfreeze.
            </param>
            <param name="toggle">
            Freeze or unfreeze entity.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetAirDragMultiplierForPlayersVehicle(CitizenFX.Core.CString)">
            <summary>
            GET_AIR_DRAG_MULTIPLIER_FOR_PLAYERS_VEHICLE
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetAllObjects">
            <summary>
            Returns all object handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
            <returns>
            An object containing a list of object handles.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetAllPeds">
            <summary>
            Returns all peds handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
            <returns>
            An object containing a list of peds handles.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetAllVehicles">
            <summary>
            Returns all vehicle handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
            <returns>
            An object containing a list of vehicle handles.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetConsoleBuffer">
            <summary>
            Returns the current console output buffer.
            </summary>
            <returns>
            The most recent game console output, as a string.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetConvar(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Can be used to get a console variable of type `char*`, for example a string.
            </summary>
            <param name="varName">
            The console variable to look up.
            </param>
            <param name="default_">
            The default value to set if none is found.
            </param>
            <returns>
            Returns the convar value if it can be found, otherwise it returns the assigned `default`.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetConvarInt(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Can be used to get a console variable casted back to `int` (an integer value).
            </summary>
            <param name="varName">
            The console variable to look up.
            </param>
            <param name="default_">
            The default value to set if none is found (variable not set using [SET_CONVAR](#\_0x341B16D2), or not accessible).
            </param>
            <returns>
            Returns the convar value if it can be found, otherwise it returns the assigned `default`.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetCurrentResourceName">
            <summary>
            Returns the name of the currently executing resource.
            </summary>
            <returns>
            The name of the resource.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityAttachedTo(System.Int32)">
            <summary>
            Gets the entity that this entity is attached to.
            </summary>
            <param name="entity">
            The entity to check.
            </param>
            <returns>
            The attached entity handle. 0 returned if the entity is not attached.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityCollisionDisabled(System.Int32)">
            <summary>
            GET_ENTITY_COLLISION_DISABLED
            </summary>
            <param name="entity">
            The target entity.
            </param>
            <returns>
            Returns whether or not entity collisions are disabled.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityCoords(System.Int32)">
            <summary>
            Gets the current coordinates for a specified entity. This native is used server side when using OneSync.
            
            See [GET_ENTITY_COORDS](#\_0x3FEF770D40960D5A) for client side.
            </summary>
            <param name="entity">
            The entity to get the coordinates from.
            </param>
            <returns>
            The current entity coordinates.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityFromStateBagName(CitizenFX.Core.CString)">
            <summary>
            Returns the entity handle for the specified state bag name. For use with [ADD_STATE_BAG_CHANGE_HANDLER](?\_0x5BA35AAF).
            </summary>
            <param name="bagName">
            An internal state bag ID from the argument to a state bag change handler.
            </param>
            <returns>
            The entity handle or 0 if the state bag name did not refer to an entity, or the entity does not exist.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityHeading(System.Int32)">
            <summary>
            GET_ENTITY_HEADING
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityHealth(System.Int32)">
            <summary>
            Only works for vehicle and peds
            </summary>
            <param name="entity">
            The entity to check the health of
            </param>
            <returns>
            If the entity is a vehicle it will return 0-1000
            If the entity is a ped it will return 0-200
            If the entity is an object it will return 0
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityMaxHealth(System.Int32)">
            <summary>
            Currently it only works with peds.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityModel(System.Int32)">
            <summary>
            GET_ENTITY_MODEL
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityPopulationType(System.Int32)">
            <summary>
            This native gets an entity's population type.
            </summary>
            <param name="entity">
            the entity to obtain the population type from
            </param>
            <returns>
            Returns the population type ID, defined by the below enumeration:```cpp
            enum ePopulationType
            {
            	POPTYPE_UNKNOWN = 0,
            	POPTYPE_RANDOM_PERMANENT,
            	POPTYPE_RANDOM_PARKED,
            	POPTYPE_RANDOM_PATROL,
            	POPTYPE_RANDOM_SCENARIO,
            	POPTYPE_RANDOM_AMBIENT,
            	POPTYPE_PERMANENT,
            	POPTYPE_MISSION,
            	POPTYPE_REPLAY,
            	POPTYPE_CACHE,
            	POPTYPE_TOOL
            };
            ```
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityRotation(System.Int32)">
            <summary>
            GET_ENTITY_ROTATION
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityRotationVelocity(System.Int32)">
            <summary>
            GET_ENTITY_ROTATION_VELOCITY
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityRoutingBucket(System.Int32)">
            <summary>
            Gets the routing bucket for the specified entity.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
            <param name="entity">
            The entity to get the routing bucket for.
            </param>
            <returns>
            The routing bucket ID.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityScript(System.Int32)">
            <summary>
            GET_ENTITY_SCRIPT
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntitySpeed(System.Int32)">
            <summary>
            Gets the current speed of the entity in meters per second.
            
            ```
            To convert to MPH: speed * 2.236936
            To convert to KPH: speed * 3.6
            ```
            </summary>
            <param name="entity">
            The entity to get the speed of
            </param>
            <returns>
            The speed of the entity in meters per second
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityType(System.Int32)">
            <summary>
            Gets the entity type (as an integer), which can be one of the following defined down below:
            
            **The following entities will return type `1`:**
            
            *   Ped
            *   Player
            *   Animal (Red Dead Redemption 2)
            *   Horse (Red Dead Redemption 2)
            
            **The following entities will return type `2`:**
            
            *   Automobile
            *   Bike
            *   Boat
            *   Heli
            *   Plane
            *   Submarine
            *   Trailer
            *   Train
            *   DraftVeh (Red Dead Redemption 2)
            
            **The following entities will return type `3`:**
            
            *   Object
            *   Door
            *   Pickup
            
            Otherwise, a value of `0` will be returned.
            </summary>
            <param name="entity">
            The entity to get the type of.
            </param>
            <returns>
            The entity type returned as an integer value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetEntityVelocity(System.Int32)">
            <summary>
            GET_ENTITY_VELOCITY
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetGameBuildNumber">
            <summary>
            Returns the internal build number of the current game being executed.
            
            Possible values:
            
            *   FiveM
                *   1604
                *   2060
                *   2189
                *   2372
                *   2545
                *   2612
                *   2699
                *   2802
                *   2944
                *   3095
            *   RedM
                *   1311
                *   1355
                *   1436
                *   1491
            *   LibertyM
                *   43
            *   FXServer
                *   0
            </summary>
            <returns>
            The build number, or **0** if no build number is known.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetGameName">
            <summary>
            Returns the current game being executed.
            
            Possible values:
            
            | Return value | Meaning                        |
            | ------------ | ------------------------------ |
            | `fxserver`   | Server-side code ('Duplicity') |
            | `fivem`      | FiveM for GTA V                |
            | `libertym`   | LibertyM for GTA IV            |
            | `redm`       | RedM for Red Dead Redemption 2 |
            </summary>
            <returns>
            The game the script environment is running in.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetGameTimer">
            <summary>
            Gets the current game timer in milliseconds.
            </summary>
            <returns>
            The game time.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHashKey(CitizenFX.Core.CString)">
            <summary>
            This native converts the passed string to a hash.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliMainRotorHealth(System.Int32)">
            <summary>
            GET_HELI_MAIN_ROTOR_HEALTH
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            See the client-side [GET_HELI_MAIN_ROTOR_HEALTH](https://docs.fivem.net/natives/?\_0xE4CB7541F413D2C5) for the return value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHeliTailRotorHealth(System.Int32)">
            <summary>
            GET_HELI_TAIL_ROTOR_HEALTH
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            See the client-side [GET_HELI_TAIL_ROTOR_HEALTH](https://docs.fivem.net/natives/?\_0xAE8CE82A4219AC8C) for the return value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetHostId">
            <summary>
            GET_HOST_ID
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetInstanceId">
            <summary>
            GET_INSTANCE_ID
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetInvokingResource">
            <summary>
            GET_INVOKING_RESOURCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetIsVehicleEngineRunning(System.Int32)">
            <summary>
            GET_IS_VEHICLE_ENGINE_RUNNING
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetIsVehiclePrimaryColourCustom(System.Int32)">
            <summary>
            GET_IS_VEHICLE_PRIMARY_COLOUR_CUSTOM
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetIsVehicleSecondaryColourCustom(System.Int32)">
            <summary>
            GET_IS_VEHICLE_SECONDARY_COLOUR_CUSTOM
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetLandingGearState(System.Int32)">
            <summary>
            See the client-side [GET_LANDING_GEAR_STATE](#\_0x9B0F3DCA3DB0F4CD) native for a description of landing gear states.
            </summary>
            <param name="vehicle">
            The vehicle to check.
            </param>
            <returns>
            The current state of the vehicles landing gear.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetLastPedInVehicleSeat(System.Int32,System.Int32)">
            <summary>
            GET_LAST_PED_IN_VEHICLE_SEAT
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <param name="seatIndex">
            See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).
            </param>
            <returns>
            The last ped in the specified seat of the passed vehicle. Returns 0 if the specified seat was never occupied or the last ped does not exist anymore.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNumPlayerIdentifiers(CitizenFX.Core.CString)">
            <summary>
            GET_NUM_PLAYER_IDENTIFIERS
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNumPlayerIndices">
            <summary>
            GET_NUM_PLAYER_INDICES
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNumPlayerTokens(CitizenFX.Core.CString)">
            <summary>
            GET_NUM_PLAYER_TOKENS
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNumResourceMetadata(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Gets the amount of metadata values with the specified key existing in the specified resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/resources/manifest/)
            </summary>
            <param name="resourceName">
            The resource name.
            </param>
            <param name="metadataKey">
            The key to look up in the resource manifest.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetNumResources">
            <summary>
            GET_NUM_RESOURCES
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPasswordHash(CitizenFX.Core.CString)">
            <summary>
            GET_PASSWORD_HASH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedArmour(System.Int32)">
            <summary>
            GET_PED_ARMOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedCauseOfDeath(System.Int32)">
            <summary>
            GET_PED_CAUSE_OF_DEATH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedDesiredHeading(System.Int32)">
            <summary>
            GET_PED_DESIRED_HEADING
            </summary>
            <param name="ped">
            The target ped
            </param>
            <returns>
            Returns ped's desired heading.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedInVehicleSeat(System.Int32,System.Int32)">
            <summary>
            GET_PED_IN_VEHICLE_SEAT
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <param name="seatIndex">
            See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).
            </param>
            <returns>
            The ped in the specified seat of the passed vehicle. Returns 0 if the specified seat is not occupied.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedMaxHealth(System.Int32)">
            <summary>
            GET_PED_MAX_HEALTH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedScriptTaskCommand(System.Int32)">
            <summary>
            Gets the script task command currently assigned to the ped.
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            The script task command currently assigned to the ped. A value of 0x811E343C denotes no script task is assigned.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedScriptTaskStage(System.Int32)">
            <summary>
            Gets the stage of the peds scripted task.
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            The stage of the ped's scripted task. A value of 3 denotes no script task is assigned.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedSourceOfDamage(System.Int32)">
            <summary>
            Get the last entity that damaged the ped. This native is used server side when using OneSync.
            </summary>
            <param name="ped">
            The target ped
            </param>
            <returns>
            The entity id. Returns 0 if the ped has not been damaged recently.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedSourceOfDeath(System.Int32)">
            <summary>
            Get the entity that killed the ped. This native is used server side when using OneSync.
            </summary>
            <param name="ped">
            The target ped
            </param>
            <returns>
            The entity id. Returns 0 if the ped has no killer.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedSpecificTaskType(System.Int32,System.Int32)">
            <summary>
            Gets the type of a ped's specific task given an index of the CPedTaskSpecificDataNode nodes.
            A ped will typically have a task at index 0, if a ped has multiple tasks at once they will be in the order 0, 1, 2, etc.
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <param name="index">
            A zero-based index with a maximum value of 7.
            </param>
            <returns>
            The type of the specific task.
            1604: A value of 530 denotes no script task is assigned or an invalid input was given.
            2060+: A value of 531 denotes no script task is assigned or an invalid input was given.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPedStealthMovement(System.Int32)">
            <summary>
            GET_PED_STEALTH_MOVEMENT
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped is stealthy.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerCameraRotation(CitizenFX.Core.CString)">
            <summary>
            Gets the current camera rotation for a specified player. This native is used server side when using OneSync.
            </summary>
            <param name="playerSrc">
            The player handle.
            </param>
            <returns>
            The player's camera rotation. Values are in radians.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerEndpoint(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_ENDPOINT
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerFakeWantedLevel(CitizenFX.Core.CString)">
            <summary>
            Gets the current fake wanted level for a specified player. This native is used server side when using OneSync.
            </summary>
            <param name="playerSrc">
            The target player
            </param>
            <returns>
            The fake wanted level
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerFromIndex(System.Int32)">
            <summary>
            GET_PLAYER_FROM_INDEX
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerFromStateBagName(CitizenFX.Core.CString)">
            <summary>
            On the server this will return the players source, on the client it will return the player handle.
            </summary>
            <param name="bagName">
            An internal state bag ID from the argument to a state bag change handler.
            </param>
            <returns>
            The player handle or 0 if the state bag name did not refer to a player, or the player does not exist.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerGuid(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_GUID
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerIdentifier(CitizenFX.Core.CString,System.Int32)">
            <summary>
            GET_PLAYER_IDENTIFIER
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerIdentifierByType(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Get an identifier from a player by the type of the identifier.
            </summary>
            <param name="playerSrc">
            The player to get the identifier for
            </param>
            <param name="identifierType">
            The string to match in an identifier, this can be `"license"` for example.
            </param>
            <returns>
            The identifier that matches the string provided
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerInvincible(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_INVINCIBLE
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <returns>
            A boolean to tell if the player is invincible.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerLastMsg(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_LAST_MSG
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerMaxArmour(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_MAX_ARMOUR
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerMaxHealth(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_MAX_HEALTH
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerMeleeWeaponDamageModifier(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER](#\_0x4A3DC7ECCC321032).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            Returns player melee weapon damage modifier value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerName(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_NAME
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerPed(CitizenFX.Core.CString)">
            <summary>
            Used to get the player's Ped Entity ID when a valid `playerSrc` is passed.
            </summary>
            <param name="playerSrc">
            The player source, passed as a string.
            </param>
            <returns>
            Returns a valid Ped Entity ID if the passed `playerSrc` is valid, `0` if not.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerPing(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_PING
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerRoutingBucket(CitizenFX.Core.CString)">
            <summary>
            Gets the routing bucket for the specified player.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
            <param name="playerSrc">
            The player to get the routing bucket for.
            </param>
            <returns>
            The routing bucket ID.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerTeam(CitizenFX.Core.CString)">
            <summary>
            GET_PLAYER_TEAM
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerTimeInPursuit(CitizenFX.Core.CString,System.Boolean)">
            <summary>
            Gets the amount of time player has spent evading the cops.
            Counter starts and increments only when cops are chasing the player.
            If the player is evading, the timer will pause.
            </summary>
            <param name="playerSrc">
            The target player
            </param>
            <param name="lastPursuit">
            False = CurrentPursuit, True = LastPursuit
            </param>
            <returns>
            Returns -1, if the player is not wanted or wasn't in pursuit before, depending on the lastPursuit parameter
            Returns 0, if lastPursuit == False and the player has a wanted level, but the pursuit has not started yet
            Otherwise, will return the milliseconds of the pursuit.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerToken(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Gets a player's token. Tokens can be used to enhance banning logic, however are specific to a server.
            </summary>
            <param name="playerSrc">
            A player.
            </param>
            <param name="index">
            Index between 0 and GET_NUM_PLAYER_TOKENS.
            </param>
            <returns>
            A token value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerWantedCentrePosition(CitizenFX.Core.CString)">
            <summary>
            Gets the current known coordinates for the specified player from cops perspective. This native is used server side when using OneSync.
            </summary>
            <param name="playerSrc">
            The target player
            </param>
            <returns>
            The player's position known by police. Vector zero if the player has no wanted level.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerWantedLevel(CitizenFX.Core.CString)">
            <summary>
            Returns given players wanted level server-side.
            </summary>
            <param name="playerSrc">
            The target player
            </param>
            <returns>
            The wanted level
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerWeaponDamageModifier(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DAMAGE_MODIFIER](#\_0xCE07B9F7817AADA3).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            The value of player weapon damage modifier.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerWeaponDefenseModifier(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DEFENSE_MODIFIER](#\_0x2D83BC011CA14A3C).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            The value of player weapon defense modifier.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetPlayerWeaponDefenseModifier_2(CitizenFX.Core.CString)">
            <summary>
            A getter for [\_SET_PLAYER_WEAPON_DEFENSE_MODIFIER\_2](#\_0xBCFDE9EDE4CF27DC).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            The value of player weapon defense modifier 2.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetRegisteredCommands">
            <summary>
            Returns all commands that are registered in the command system.
            The data returned adheres to the following layout:
            
            ```
            [
            {
            "name": "cmdlist"
            },
            {
            "name": "command1"
            }
            ]
            ```
            </summary>
            <returns>
            An object containing registered commands.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceByFindIndex(System.Int32)">
            <summary>
            GET_RESOURCE_BY_FIND_INDEX
            </summary>
            <param name="findIndex">
            The index of the resource (starting at 0)
            </param>
            <returns>
            The resource name as a `string`
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceKvpFloat(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938).
            </summary>
            <param name="key">
            The key to fetch
            </param>
            <returns>
            The floating-point value stored under the specified key, or 0.0 if not found.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceKvpInt(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8).
            </summary>
            <param name="key">
            The key to fetch
            </param>
            <returns>
            The integer value stored under the specified key, or 0 if not found.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceKvpString(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B).
            </summary>
            <param name="key">
            The key to fetch
            </param>
            <returns>
            The string value stored under the specified key, or nil/null if not found.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceMetadata(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32)">
            <summary>
            Gets the metadata value at a specified key/index from a resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/resources/manifest/)
            </summary>
            <param name="resourceName">
            The resource name.
            </param>
            <param name="metadataKey">
            The key in the resource manifest.
            </param>
            <param name="index">
            The value index, in a range from \[0..GET_NUM_RESOURCE_METDATA-1].
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourcePath(CitizenFX.Core.CString)">
            <summary>
            Returns the physical on-disk path of the specified resource.
            </summary>
            <param name="resourceName">
            The name of the resource.
            </param>
            <returns>
            The resource directory name, possibly without trailing slash.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetResourceState(CitizenFX.Core.CString)">
            <summary>
            Returns the current state of the specified resource.
            </summary>
            <param name="resourceName">
            The name of the resource.
            </param>
            <returns>
            The resource state. One of `"missing", "started", "starting", "stopped", "stopping", "uninitialized" or "unknown"`.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetSelectedPedWeapon(System.Int32)">
            <summary>
            Returns a hash of selected ped weapon.
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            The weapon hash.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetStateBagValue(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Returns the value of a state bag key.
            </summary>
            <returns>
            Value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetTrainCarriageEngine(System.Int32)">
            <summary>
            GET_TRAIN_CARRIAGE_ENGINE
            </summary>
            <param name="train">
            The entity handle.
            </param>
            <returns>
            The train engine carriage.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetTrainCarriageIndex(System.Int32)">
            <summary>
            GET_TRAIN_CARRIAGE_INDEX
            </summary>
            <param name="train">
            The entity handle.
            </param>
            <returns>
            The carriage index. -1 returned if invalid result.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleBodyHealth(System.Int32)">
            <summary>
            GET_VEHICLE_BODY_HEALTH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleColours(System.Int32,System.Int32@,System.Int32@)">
            <summary>
            GET_VEHICLE_COLOURS
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleCustomPrimaryColour(System.Int32,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            GET_VEHICLE_CUSTOM_PRIMARY_COLOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleCustomSecondaryColour(System.Int32,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            GET_VEHICLE_CUSTOM_SECONDARY_COLOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleDashboardColour(System.Int32,System.Int32@)">
            <summary>
            GET_VEHICLE_DASHBOARD_COLOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleDirtLevel(System.Int32)">
            <summary>
            GET_VEHICLE_DIRT_LEVEL
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleDoorLockStatus(System.Int32)">
            <summary>
            ```lua
            enum_VehicleLockStatus = {
                None = 0,
                Locked = 2,
                LockedForPlayer = 3,
                StickPlayerInside = 4, -- Doesn't allow players to exit the vehicle with the exit vehicle key.
                CanBeBrokenInto = 7, -- Can be broken into the car. If the glass is broken, the value will be set to 1
                CanBeBrokenIntoPersist = 8, -- Can be broken into persist
                CannotBeTriedToEnter = 10, -- Cannot be tried to enter (Nothing happens when you press the vehicle enter key).
            }
            ```
            
            It should be [noted](https://forum.cfx.re/t/4863241) that while the [client-side command](#\_0x25BC98A59C2EA962) and its
            setter distinguish between states 0 (unset) and 1 (unlocked), the game will synchronize both as state 0, so the server-side
            command will return only '0' if unlocked.
            </summary>
            <param name="vehicle">
            A vehicle handle.
            </param>
            <returns>
            The door lock status for the specified vehicle.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleDoorStatus(System.Int32)">
            <summary>
            GET_VEHICLE_DOOR_STATUS
            </summary>
            <returns>
            A number from 0 to 7.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleDoorsLockedForPlayer(System.Int32)">
            <summary>
            Currently it only works when set to "all players".
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleEngineHealth(System.Int32)">
            <summary>
            GET_VEHICLE_ENGINE_HEALTH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleExtraColours(System.Int32,System.Int32@,System.Int32@)">
            <summary>
            GET_VEHICLE_EXTRA_COLOURS
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleFlightNozzlePosition(System.Int32)">
            <summary>
            Gets the flight nozzel position for the specified vehicle. See the client-side [\_GET_VEHICLE_FLIGHT_NOZZLE_POSITION](#\_0xDA62027C8BDB326E) native for usage examples.
            </summary>
            <param name="vehicle">
            The vehicle to check.
            </param>
            <returns>
            The flight nozzel position between 0.0 (flying normally) and 1.0 (VTOL mode)
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleHandbrake(System.Int32)">
            <summary>
            GET_VEHICLE_HANDBRAKE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleHeadlightsColour(System.Int32)">
            <summary>
            GET_VEHICLE_HEADLIGHTS_COLOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleHomingLockonState(System.Int32)">
            <summary>
            Gets the lock on state for the specified vehicle. See the client-side [GET_VEHICLE_HOMING_LOCKON_STATE](#\_0xE6B0E8CFC3633BF0) native for a description of lock on states.
            </summary>
            <param name="vehicle">
            The vehicle to check.
            </param>
            <returns>
            The lock on state.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleInteriorColour(System.Int32,System.Int32@)">
            <summary>
            GET_VEHICLE_INTERIOR_COLOUR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleLightsState(System.Int32,System.Boolean@,System.Boolean@)">
            <summary>
            GET_VEHICLE_LIGHTS_STATE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleLivery(System.Int32)">
            <summary>
            GET_VEHICLE_LIVERY
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleLockOnTarget(System.Int32)">
            <summary>
            Gets the vehicle that is locked on to for the specified vehicle.
            </summary>
            <param name="vehicle">
            The vehicle to check.
            </param>
            <returns>
            The vehicle that is locked on. 0 returned if no vehicle is locked on.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleNumberPlateText(System.Int32)">
            <summary>
            GET_VEHICLE_NUMBER_PLATE_TEXT
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleNumberPlateTextIndex(System.Int32)">
            <summary>
            GET_VEHICLE_NUMBER_PLATE_TEXT_INDEX
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehiclePedIsIn(System.Int32,System.Boolean)">
            <summary>
            Gets the vehicle the specified Ped is/was in depending on bool value. This native is used server side when using OneSync.
            </summary>
            <param name="ped">
            The target ped
            </param>
            <param name="lastVehicle">
            False = CurrentVehicle, True = LastVehicle
            </param>
            <returns>
            The vehicle id. Returns 0 if the ped is/was not in a vehicle.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehiclePetrolTankHealth(System.Int32)">
            <summary>
            GET_VEHICLE_PETROL_TANK_HEALTH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleRadioStationIndex(System.Int32)">
            <summary>
            GET_VEHICLE_RADIO_STATION_INDEX
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleRoofLivery(System.Int32)">
            <summary>
            GET_VEHICLE_ROOF_LIVERY
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleSteeringAngle(System.Int32)">
            <summary>
            GET_VEHICLE_STEERING_ANGLE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleType(System.Int32)">
            <summary>
            Returns the type of the passed vehicle.
            
            ### Vehicle types
            
            *   automobile
            *   bike
            *   boat
            *   heli
            *   plane
            *   submarine
            *   trailer
            *   train
            </summary>
            <param name="vehicle">
            The vehicle's entity handle.
            </param>
            <returns>
            If the entity is a vehicle, the vehicle type. If it is not a vehicle, the return value will be null.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleTyreSmokeColor(System.Int32,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            GET_VEHICLE_TYRE_SMOKE_COLOR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleWheelType(System.Int32)">
            <summary>
            GET_VEHICLE_WHEEL_TYPE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GetVehicleWindowTint(System.Int32)">
            <summary>
            GET_VEHICLE_WINDOW_TINT
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GiveWeaponComponentToPed(System.Int32,System.UInt32,System.UInt32)">
            <summary>
            GIVE_WEAPON_COMPONENT_TO_PED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.GiveWeaponToPed(System.Int32,System.UInt32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            GIVE_WEAPON_TO_PED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.HasEntityBeenMarkedAsNoLongerNeeded(System.Int32)">
            <summary>
            HAS_ENTITY_BEEN_MARKED_AS_NO_LONGER_NEEDED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.HasVehicleBeenDamagedByBullets(System.Int32)">
            <summary>
            HAS_VEHICLE_BEEN_DAMAGED_BY_BULLETS
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            Returns whether or not the target vehicle has been damaged by bullets.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.HasVehicleBeenOwnedByPlayer(System.Int32)">
            <summary>
            HAS_VEHICLE_BEEN_OWNED_BY_PLAYER
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.InvokeFunctionReference(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32,System.Int32@)">
            <summary>
            INVOKE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsAceAllowed(CitizenFX.Core.CString)">
            <summary>
            IS_ACE_ALLOWED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsBoatAnchoredAndFrozen(System.Int32)">
            <summary>
            IS_BOAT_ANCHORED_AND_FROZEN
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            Returns whether or not the boat is anchored and frozen.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsBoatWrecked(System.Int32)">
            <summary>
            IS_BOAT_WRECKED
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <returns>
            Returns whether or not the boat is wrecked.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsDuplicityVersion">
            <summary>
            Gets whether or not this is the CitizenFX server.
            </summary>
            <returns>
            A boolean value.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsEntityPositionFrozen(System.Int32)">
            <summary>
            A getter for [FREEZE_ENTITY_POSITION](#\_0x428CA6DBD1094446).
            </summary>
            <param name="entity">
            The entity to check for
            </param>
            <returns>
            Boolean stating if it is frozen or not.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsEntityVisible(System.Int32)">
            <summary>
            This native checks if the given entity is visible.
            </summary>
            <returns>
            Returns `true` if the entity is visible, `false` otherwise.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsFlashLightOn(System.Int32)">
            <summary>
            IS_FLASH_LIGHT_ON
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped's flash light is on.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPedAPlayer(System.Int32)">
            <summary>
            This native checks if the given ped is a player.
            </summary>
            <returns>
            Returns `true` if the ped is a player, `false` otherwise.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPedHandcuffed(System.Int32)">
            <summary>
            IS_PED_HANDCUFFED
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped is handcuffed.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPedRagdoll(System.Int32)">
            <summary>
            IS_PED_RAGDOLL
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped is ragdolling.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPedStrafing(System.Int32)">
            <summary>
            IS_PED_STRAFING
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped is strafing.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPedUsingActionMode(System.Int32)">
            <summary>
            IS_PED_USING_ACTION_MODE
            </summary>
            <param name="ped">
            The target ped.
            </param>
            <returns>
            Whether or not the ped is using action mode.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerAceAllowed(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            IS_PLAYER_ACE_ALLOWED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerCommerceInfoLoaded(CitizenFX.Core.CString)">
            <summary>
            Requests whether or not the commerce data for the specified player has loaded.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerCommerceInfoLoadedExt(CitizenFX.Core.CString)">
            <summary>
            Requests whether or not the commerce data for the specified player has loaded from Tebex.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerEvadingWantedLevel(CitizenFX.Core.CString)">
            <summary>
            This will return true if the player is evading wanted level, meaning that the wanted level stars are blink.
            Otherwise will return false.
            
            If the player is not wanted, it simply returns false.
            </summary>
            <param name="playerSrc">
            The target player
            </param>
            <returns>
            boolean value, depending if the player is evading his wanted level or not.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPlayerUsingSuperJump(CitizenFX.Core.CString)">
            <summary>
            IS_PLAYER_USING_SUPER_JUMP
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsPrincipalAceAllowed(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            IS_PRINCIPAL_ACE_ALLOWED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsVehicleEngineStarting(System.Int32)">
            <summary>
            IS_VEHICLE_ENGINE_STARTING
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsVehicleExtraTurnedOn(System.Int32,System.Int32)">
            <summary>
            IS_VEHICLE_EXTRA_TURNED_ON
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsVehicleSirenOn(System.Int32)">
            <summary>
            IS_VEHICLE_SIREN_ON
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsVehicleTyreBurst(System.Int32,System.Int32,System.Boolean)">
            <summary>
            IS_VEHICLE_TYRE_BURST
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.IsVehicleWindowIntact(System.Int32,System.Int32)">
            <summary>
            See the client-side [IS_VEHICLE_WINDOW_INTACT](https://docs.fivem.net/natives/?\_0x46E571A0E20D01F1) for a window indexes list.
            </summary>
            <param name="vehicle">
            The target vehicle.
            </param>
            <param name="windowIndex">
            The window index.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.LoadPlayerCommerceData(CitizenFX.Core.CString)">
            <summary>
            Requests the commerce data for the specified player, including the owned SKUs. Use `IS_PLAYER_COMMERCE_INFO_LOADED` to check if it has loaded.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.LoadPlayerCommerceDataExt(CitizenFX.Core.CString)">
            <summary>
            Requests the commerce data from Tebex for the specified player, including the owned SKUs. Use `IS_PLAYER_COMMERCE_INFO_LOADED` to check if it has loaded.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.LoadResourceFile(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Reads the contents of a text file in a specified resource.
            If executed on the client, this file has to be included in `files` in the resource manifest.
            Example: `local data = LoadResourceFile("devtools", "data.json")`
            </summary>
            <param name="resourceName">
            The resource name.
            </param>
            <param name="fileName">
            The file in the resource.
            </param>
            <returns>
            The file contents
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.MumbleCreateChannel(System.Int32)">
            <summary>
            Create a permanent voice channel.
            </summary>
            <param name="id">
            ID of the channel.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.MumbleIsPlayerMuted(System.Int32)">
            <summary>
            Checks if the player is currently muted
            </summary>
            <param name="playerSrc">
            The player to get the mute state for
            </param>
            <returns>
            Whether or not the player is muted
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.MumbleSetPlayerMuted(System.Int32,System.Boolean)">
            <summary>
            Mutes or unmutes the specified player
            </summary>
            <param name="playerSrc">
            The player to set the mute state of
            </param>
            <param name="toggle">
            Whether to mute or unmute the player
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.NetworkGetEntityFromNetworkId(System.Int32)">
            <summary>
            NETWORK_GET_ENTITY_FROM_NETWORK_ID
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.NetworkGetEntityOwner(System.Int32)">
            <summary>
            Returns the owner ID of the specified entity.
            </summary>
            <param name="entity">
            The entity to get the owner for.
            </param>
            <returns>
            On the server, the server ID of the entity owner. On the client, returns the player/slot ID of the entity owner.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.NetworkGetFirstEntityOwner(System.Int32)">
            <summary>
            Returns the first owner ID of the specified entity.
            </summary>
            <param name="entity">
            The entity to get the first owner for.
            </param>
            <returns>
            The server ID of the first entity owner.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.NetworkGetNetworkIdFromEntity(System.Int32)">
            <summary>
            NETWORK_GET_NETWORK_ID_FROM_ENTITY
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.NetworkGetVoiceProximityOverrideForPlayer(CitizenFX.Core.CString)">
            <summary>
            NETWORK_GET_VOICE_PROXIMITY_OVERRIDE_FOR_PLAYER
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.PerformHttpRequestInternal(CitizenFX.Core.CString,System.Int32)">
            <summary>
            PERFORM_HTTP_REQUEST_INTERNAL
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.PerformHttpRequestInternalEx(System.Object)">
            <summary>
            PERFORM_HTTP_REQUEST_INTERNAL_EX
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.PrintStructuredTrace(CitizenFX.Core.CString)">
            <summary>
            Prints 'structured trace' data to the server `file descriptor 3` channel. This is not generally useful outside of
            server monitoring utilities.
            </summary>
            <param name="jsonString">
            JSON data to submit as `payload` in the `script_structured_trace` event.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ProfilerEnterScope(CitizenFX.Core.CString)">
            <summary>
            Scope entry for profiler.
            </summary>
            <param name="scopeName">
            Scope name.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ProfilerExitScope">
            <summary>
            Scope exit for profiler.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ProfilerIsRecording">
            <summary>
            Returns true if the profiler is active.
            </summary>
            <returns>
            True or false.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RegisterCommand(CitizenFX.Core.CString,System.Delegate,System.Boolean)">
            <summary>
            Registered commands can be executed by entering them in the client console (this works for client side and server side registered commands). Or by entering them in the server console/through an RCON client (only works for server side registered commands). Or if you use a supported chat resource, like the default one provided in the cfx-server-data repository, then you can enter the command in chat by prefixing it with a `/`.
            
            Commands registered using this function can also be executed by resources, using the [`ExecuteCommand` native](#\_0x561C060B).
            
            The restricted bool is not used on the client side. Permissions can only be checked on the server side, so if you want to limit your command with an ace permission automatically, make it a server command (by registering it in a server script).
            
            **Example result**:
            
            ![](https://i.imgur.com/TaCnG09.png)
            </summary>
            <param name="commandName">
            The command you want to register.
            </param>
            <param name="handler">
            A handler function that gets called whenever the command is executed.
            </param>
            <param name="restricted">
            If this is a server command and you set this to true, then players will need the command.yourCommandName ace permission to execute this command.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RegisterConsoleListener(System.Delegate)">
            <summary>
            Registers a listener for console output messages.
            </summary>
            <param name="listener">
            A function of `(channel: string, message: string) =&gt; void`. The message might contain `\n`.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RegisterResourceAsEventHandler(CitizenFX.Core.CString)">
            <summary>
            An internal function which allows the current resource's HLL script runtimes to receive state for the specified event.
            </summary>
            <param name="eventName">
            An event name, or "\*" to disable HLL event filtering for this resource.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RegisterResourceAsset(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
            
            Registers a cached resource asset with the resource system, similar to the automatic scanning of the `stream/` folder.
            </summary>
            <param name="resourceName">
            The resource to add the asset to.
            </param>
            <param name="fileName">
            A file name in the resource.
            </param>
            <returns>
            A cache string to pass to `REGISTER_STREAMING_FILE_FROM_CACHE` on the client.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RegisterResourceBuildTaskFactory(CitizenFX.Core.CString,System.Delegate)">
            <summary>
            Registers a build task factory for resources.
            The function should return an object (msgpack map) with the following fields:
            
            ```
            {
            // returns whether the specific resource should be built
            shouldBuild = func(resourceName: string): bool,
            
            // asynchronously start building the specific resource.
            // call cb when completed
            build = func(resourceName: string, cb: func(success: bool, status: string): void): void
            }
            ```
            </summary>
            <param name="factoryId">
            The identifier for the build task.
            </param>
            <param name="factoryFn">
            The factory function.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveAllPedWeapons(System.Int32,System.Boolean)">
            <summary>
            Parameter `p1` does not seem to be used or referenced in game binaries.\
            **Note:** When called for networked entities, a `CRemoveAllWeaponsEvent` will be created per request.
            </summary>
            <param name="ped">
            The ped entity
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveBlip(System.Int32@)">
            <summary>
            Removes the blip from your map.
            </summary>
            <param name="blip">
            Blip handle to remove.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveStateBagChangeHandler(System.Int32)">
            <summary>
            **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
            
            Removes a handler for changes to a state bag.
            </summary>
            <param name="cookie">
            The cookie.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveWeaponComponentFromPed(System.Int32,System.UInt32,System.UInt32)">
            <summary>
            REMOVE_WEAPON_COMPONENT_FROM_PED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RemoveWeaponFromPed(System.Int32,System.UInt32)">
            <summary>
            This native removes a specified weapon from your selected ped.  
            Weapon Hashes: pastebin.com/0wwDZgkF  
            Example:  
            C#:  
            Function.Call(Hash.REMOVE_WEAPON_FROM_PED, Game.Player.Character, 0x99B507EA);  
            C++:  
            WEAPON::REMOVE_WEAPON_FROM_PED(PLAYER::PLAYER_PED_ID(), 0x99B507EA);  
            The code above removes the knife from the player.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.RequestPlayerCommerceSession(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Requests the specified player to buy the passed SKU. This'll pop up a prompt on the client, which upon acceptance
            will open the browser prompting further purchase details.
            </summary>
            <param name="playerSrc">
            The player handle
            </param>
            <param name="skuId">
            The ID of the SKU.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SaveResourceFile(CitizenFX.Core.CString,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32)">
            <summary>
            Writes the specified data to a file in the specified resource.
            Using a length of `-1` will automatically detect the length assuming the data is a C string.
            </summary>
            <param name="resourceName">
            The name of the resource.
            </param>
            <param name="fileName">
            The name of the file.
            </param>
            <param name="data">
            The data to write to the file.
            </param>
            <param name="dataLength">
            The length of the written data.
            </param>
            <returns>
            A value indicating if the write succeeded.
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ScanResourceRoot(CitizenFX.Core.CString,System.Delegate)">
            <summary>
            Scans the resources in the specified resource root. This function is only available in the 'monitor mode' process and is
            not available for user resources.
            </summary>
            <param name="rootPath">
            The resource directory to scan.
            </param>
            <param name="callback">
            A callback that will receive an object with results.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.ScheduleResourceTick(CitizenFX.Core.CString)">
            <summary>
            Schedules the specified resource to run a tick as soon as possible, bypassing the server's fixed tick rate.
            </summary>
            <param name="resourceName">
            The resource to tick.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetBlipSprite(System.Int32,System.Int32)">
            <summary>
            Sets the displayed sprite for a specific blip.
            
            There's a [list of sprites](https://docs.fivem.net/game-references/blips/) on the FiveM documentation site.
            </summary>
            <param name="blip">
            The blip to change.
            </param>
            <param name="spriteId">
            The sprite ID to set.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetConvar(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            SET_CONVAR
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetConvarReplicated(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Used to replicate a server variable onto clients.
            </summary>
            <param name="varName">
            The console variable name.
            </param>
            <param name="_value">
            The value to set for the given console variable.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetConvarServerInfo(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            SET_CONVAR_SERVER_INFO
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetCurrentPedWeapon(System.Int32,System.UInt32,System.Boolean)">
            <summary>
            SET_CURRENT_PED_WEAPON
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityCoords(System.Int32,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Sets the coordinates (world position) for a specified entity, offset by the radius of the entity on the Z axis.
            </summary>
            <param name="entity">
            The entity to change coordinates for.
            </param>
            <param name="xPos">
            The X coordinate.
            </param>
            <param name="yPos">
            The Y coordinate.
            </param>
            <param name="zPos">
            The Z coordinate, ground level.
            </param>
            <param name="alive">
            Unused by the game, potentially used by debug builds of GTA in order to assert whether or not an entity was alive.
            </param>
            <param name="deadFlag">
            Whether to disable physics for dead peds, too, and not just living peds.
            </param>
            <param name="ragdollFlag">
            A special flag used for ragdolling peds.
            </param>
            <param name="clearArea">
            Whether to clear any entities in the target area.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityDistanceCullingRadius(System.Int32,System.Single)">
            <summary>
            It overrides the default distance culling radius of an entity. Set to `0.0` to reset.
            If you want to interact with an entity outside of your players' scopes set the radius to a huge number.
            
            **WARNING**: Culling natives are deprecated and have known, [unfixable issues](https://forum.cfx.re/t/issue-with-culling-radius-and-server-side-entities/4900677/4)
            </summary>
            <param name="entity">
            The entity handle to override the distance culling radius.
            </param>
            <param name="radius">
            The new distance culling radius.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityHeading(System.Int32,System.Single)">
            <summary>
            Set the heading of an entity in degrees also known as "Yaw".
            </summary>
            <param name="entity">
            The entity to set the heading for.
            </param>
            <param name="heading">
            The heading in degrees.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityIgnoreRequestControlFilter(System.Int32,System.Boolean)">
            <summary>
            It allows to flag an entity to ignore the request control filter policy.
            </summary>
            <param name="entity">
            The entity handle to ignore the request control filter.
            </param>
            <param name="ignore">
            Define if the entity ignores the request control filter policy.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityRotation(System.Int32,System.Single,System.Single,System.Single,System.Int32,System.Boolean)">
            <summary>
            SET_ENTITY_ROTATION
            </summary>
            <param name="rotationOrder">
            The order yaw pitch roll are applied, see [`GET_ENTITY_ROTATION`](#\_0xAFBD61CC738D9EB9).
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityRoutingBucket(System.Int32,System.Int32)">
            <summary>
            Sets the routing bucket for the specified entity.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
            <param name="entity">
            The entity to set the routing bucket for.
            </param>
            <param name="bucket">
            The bucket ID.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetEntityVelocity(System.Int32,System.Single,System.Single,System.Single)">
            <summary>
            Note that the third parameter(denoted as z) is "up and down" with positive numbers encouraging upwards movement.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetGameType(CitizenFX.Core.CString)">
            <summary>
            SET_GAME_TYPE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetHttpHandler(System.Delegate)">
            <summary>
            Sets the handler for HTTP requests made to the executing resource.
            
            Example request URL: `http://localhost:30120/http-test/ping` - this request will be sent to the `http-test` resource with the `/ping` path.
            
            The handler function assumes the following signature:
            
            ```ts
            function HttpHandler(
              request: {
                address: string;
                headers: Record&lt;string, string&gt;;
                method: string;
                path: string;
                setDataHandler(handler: (data: string) =&gt; void): void;
                setDataHandler(handler: (data: ArrayBuffer) =&gt; void, binary: 'binary'): void;
                setCancelHandler(handler: () =&gt; void): void;
              },
              response: {
                writeHead(code: number, headers?: Record&lt;string, string | string[]&gt;): void;
                write(data: string): void;
                send(data?: string): void;
              }
            ): void;
            ```
            
            *   **request**: The request object.
                *   **address**: The IP address of the request sender.
                *   **path**: The path to where the request was sent.
                *   **headers**: The headers sent with the request.
                *   **method**: The request method.
                *   **setDataHandler**: Sets the handler for when a data body is passed with the request. Additionally you can pass the `'binary'` argument to receive a `BufferArray` in JavaScript or `System.Byte[]` in C# (has no effect in Lua).
                *   **setCancelHandler**: Sets the handler for when the request is cancelled.
            *   **response**: An object to control the response.
                *   **writeHead**: Sets the status code &amp; headers of the response. Can be only called once and won't work if called after running other response functions.
                *   **write**: Writes to the response body without sending it. Can be called multiple times.
                *   **send**: Writes to the response body and then sends it along with the status code &amp; headers, finishing the request.
            </summary>
            <param name="handler">
            The handler function.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetMapName(CitizenFX.Core.CString)">
            <summary>
            SET_MAP_NAME
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedAmmo(System.Int32,System.UInt32,System.Int32)">
            <summary>
            NativeDB Added Parameter 4: BOOL p3
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedArmour(System.Int32,System.Int32)">
            <summary>
            Sets the armor of the specified ped.  
            ped: The Ped to set the armor of.  
            amount: A value between 0 and 100 indicating the value to set the Ped's armor to.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedCanRagdoll(System.Int32,System.Boolean)">
            <summary>
            SET_PED_CAN_RAGDOLL
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedComponentVariation(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            This native is used to set component variation on a ped. Components, drawables and textures IDs are related to the ped model.
            
            ### MP Freemode list of components
            
            **0**: Face
            **1**: Mask
            **2**: Hair
            **3**: Torso
            **4**: Leg
            **5**: Parachute / bag
            **6**: Shoes
            **7**: Accessory
            **8**: Undershirt
            **9**: Kevlar
            **10**: Badge
            **11**: Torso 2
            
            List of Component IDs
            
            ```cpp
            // Components
            enum ePedVarComp
            {
                PV_COMP_INVALID = 0xFFFFFFFF,
                PV_COMP_HEAD = 0, // "HEAD"
                PV_COMP_BERD = 1, // "BEARD"
                PV_COMP_HAIR = 2, // "HAIR"
                PV_COMP_UPPR = 3, // "UPPER"
                PV_COMP_LOWR = 4, // "LOWER"
                PV_COMP_HAND = 5, // "HAND"
                PV_COMP_FEET = 6, // "FEET"
                PV_COMP_TEEF = 7, // "TEETH"
                PV_COMP_ACCS = 8, // "ACCESSORIES"
                PV_COMP_TASK = 9, // "TASK"
                PV_COMP_DECL = 10, // "DECL"
                PV_COMP_JBIB = 11, // "JBIB"
                PV_COMP_MAX = 12,
            };
            ```
            </summary>
            <param name="ped">
            The ped handle.
            </param>
            <param name="componentId">
            The component that you want to set.
            </param>
            <param name="drawableId">
            The drawable id that is going to be set. Refer to [GET_NUMBER_OF_PED_DRAWABLE_VARIATIONS](#\_0x27561561732A7842).
            </param>
            <param name="textureId">
            The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_TEXTURE_VARIATIONS](#\_0x8F7156A3142A6BAD).
            </param>
            <param name="paletteId">
            0 to 3.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedConfigFlag(System.Int32,System.Int32,System.Boolean)">
            <summary>
            cpp
            // Potential names and hash collisions included as comments
            enum ePedConfigFlags {
            	_0x67D1A445 = 0,
            	_0xC63DE95E = 1,
            	CPED_CONFIG_FLAG_NoCriticalHits = 2,
            	CPED_CONFIG_FLAG_DrownsInWater = 3,
            	CPED_CONFIG_FLAG_DisableReticuleFixedLockon = 4,
            	_0x37D196F4 = 5,
            	_0xE2462399 = 6,
            	CPED_CONFIG_FLAG_UpperBodyDamageAnimsOnly = 7,
            	_0xEDDEB838 = 8,
            	_0xB398B6FD = 9,
            	_0xF6664E68 = 10,
            	_0xA05E7CA3 = 11,
            	_0xCE394045 = 12,
            	CPED_CONFIG_FLAG_NeverLeavesGroup = 13,
            	_0xCD8D1411 = 14,
            	_0xB031F1A9 = 15,
            	_0xFE65BEE3 = 16,
            	CPED_CONFIG_FLAG_BlockNonTemporaryEvents = 17,
            	_0x380165BD = 18,
            	_0x07C045C7 = 19,
            	_0x583B5E2D = 20,
            	_0x475EDA58 = 21,
            	_0x8629D05B = 22,
            	_0x1522968B = 23,
            	CPED_CONFIG_FLAG_IgnoreSeenMelee = 24,
            	_0x4CC09C4B = 25,
            	_0x034F3053 = 26,
            	_0xD91BA7CC = 27,
            	_0x5C8DC66E = 28,
            	_0x8902EAA0 = 29,
            	_0x6580B9D2 = 30,
            	_0x0EF7A297 = 31,
            	_0x6BF86E5B = 32,
            	CPED_CONFIG_FLAG_DieWhenRagdoll = 33,
            	CPED_CONFIG_FLAG_HasHelmet = 34,
            	CPED_CONFIG_FLAG_UseHelmet = 35,
            	_0xEEB3D630 = 36,
            	_0xB130D17B = 37,
            	_0x5F071200 = 38,
            	CPED_CONFIG_FLAG_DisableEvasiveDives = 39,
            	_0xC287AAFF = 40,
            	_0x203328CC = 41,
            	CPED_CONFIG_FLAG_DontInfluenceWantedLevel = 42,
            	CPED_CONFIG_FLAG_DisablePlayerLockon = 43,
            	CPED_CONFIG_FLAG_DisableLockonToRandomPeds = 44,
            	_0xEC4A8ACF = 45,
            	_0xDB115BFA = 46,
            	CPED_CONFIG_FLAG_PedBeingDeleted = 47,
            	CPED_CONFIG_FLAG_BlockWeaponSwitching = 48,
            	_0xF8E99565 = 49,
            	_0xDD17FEE6 = 50,
            	_0x7ED9B2C9 = 51,
            	_0x655E8618 = 52,
            	_0x5A6C1F6E = 53,
            	_0xD749FC41 = 54,
            	_0x357F63F3 = 55,
            	_0xC5E60961 = 56,
            	_0x29275C3E = 57,
            	CPED_CONFIG_FLAG_IsFiring = 58,
            	CPED_CONFIG_FLAG_WasFiring = 59,
            	CPED_CONFIG_FLAG_IsStanding = 60,
            	CPED_CONFIG_FLAG_WasStanding = 61,
            	CPED_CONFIG_FLAG_InVehicle = 62,
            	CPED_CONFIG_FLAG_OnMount = 63,
            	CPED_CONFIG_FLAG_AttachedToVehicle = 64,
            	CPED_CONFIG_FLAG_IsSwimming = 65,
            	CPED_CONFIG_FLAG_WasSwimming = 66,
            	CPED_CONFIG_FLAG_IsSkiing = 67,
            	CPED_CONFIG_FLAG_IsSitting = 68,
            	CPED_CONFIG_FLAG_KilledByStealth = 69,
            	CPED_CONFIG_FLAG_KilledByTakedown = 70,
            	CPED_CONFIG_FLAG_Knockedout = 71,
            	_0x3E3C4560 = 72,
            	_0x2994C7B7 = 73,
            	_0x6D59D275 = 74,
            	CPED_CONFIG_FLAG_UsingCoverPoint = 75,
            	CPED_CONFIG_FLAG_IsInTheAir = 76,
            	_0x2D493FB7 = 77,
            	CPED_CONFIG_FLAG_IsAimingGun = 78,
            	_0x14D69875 = 79,
            	_0x40B05311 = 80,
            	_0x8B230BC5 = 81,
            	_0xC74E5842 = 82,
            	_0x9EA86147 = 83,
            	_0x674C746C = 84,
            	_0x3E56A8C2 = 85,
            	_0xC144A1EF = 86,
            	_0x0548512D = 87,
            	_0x31C93909 = 88,
            	_0xA0269315 = 89,
            	_0xD4D59D4D = 90,
            	_0x411D4420 = 91,
            	_0xDF4AEF0D = 92,
            	CPED_CONFIG_FLAG_ForcePedLoadCover = 93,
            	_0x300E4CD3 = 94,
            	_0xF1C5BF04 = 95,
            	_0x89C2EF13 = 96,
            	CPED_CONFIG_FLAG_VaultFromCover = 97,
            	_0x02A852C8 = 98,
            	_0x3D9407F1 = 99,
            	_0x319B4558 = 100,
            	CPED_CONFIG_FLAG_ForcedAim = 101,
            	_0xB942D71A = 102,
            	_0xD26C55A8 = 103,
            	_0xB89E703B = 104,
            	CPED_CONFIG_FLAG_ForceReload = 105,
            	_0xD9E73DA2 = 106,
            	_0xFF71DC2C = 107,
            	_0x1E27E8D8 = 108,
            	_0xF2C53966 = 109,
            	_0xC4DBE247 = 110,
            	_0x83C0A4BF = 111,
            	_0x0E0FAF8C = 112,
            	_0x26616660 = 113,
            	_0x43B80B79 = 114,
            	_0x0D2A9309 = 115,
            	_0x12C1C983 = 116,
            	CPED_CONFIG_FLAG_BumpedByPlayer = 117,
            	_0xE586D504 = 118,
            	_0x52374204 = 119,
            	CPED_CONFIG_FLAG_IsHandCuffed = 120,
            	CPED_CONFIG_FLAG_IsAnkleCuffed = 121,
            	CPED_CONFIG_FLAG_DisableMelee = 122,
            	_0xFE714397 = 123,
            	_0xB3E660BD = 124,
            	_0x5FED6BFD = 125,
            	_0xC9D6F66F = 126,
            	_0x519BC986 = 127,
            	CPED_CONFIG_FLAG_CanBeAgitated = 128,
            	_0x9A4B617C = 129, // CPED_CONFIG_FLAG_FaceDirInsult
            	_0xDAB70E9F = 130,
            	_0xE569438A = 131,
            	_0xBBC77D6D = 132,
            	_0xCB59EF0F = 133,
            	_0x8C5EA971 = 134,
            	CPED_CONFIG_FLAG_IsScuba = 135,
            	CPED_CONFIG_FLAG_WillArrestRatherThanJack = 136,
            	_0xDCE59B58 = 137,
            	CPED_CONFIG_FLAG_RidingTrain = 138,
            	CPED_CONFIG_FLAG_ArrestResult = 139,
            	CPED_CONFIG_FLAG_CanAttackFriendly = 140,
            	_0x98A4BE43 = 141,
            	_0x6901E731 = 142,
            	_0x9EC9BF6C = 143,
            	_0x42841A8F = 144,
            	CPED_CONFIG_FLAG_ShootingAnimFlag = 145,
            	CPED_CONFIG_FLAG_DisableLadderClimbing = 146,
            	CPED_CONFIG_FLAG_StairsDetected = 147,
            	CPED_CONFIG_FLAG_SlopeDetected = 148,
            	_0x1A15670B = 149,
            	_0x61786EE5 = 150,
            	_0xCB9186BD = 151,
            	_0xF0710152 = 152,
            	_0x43DFE310 = 153,
            	_0xC43C624E = 154,
            	CPED_CONFIG_FLAG_CanPerformArrest = 155,
            	CPED_CONFIG_FLAG_CanPerformUncuff = 156,
            	CPED_CONFIG_FLAG_CanBeArrested = 157,
            	_0xF7960FF5 = 158,
            	_0x59564113 = 159,
            	_0x0C6C3099 = 160,
            	_0x645F927A = 161,
            	_0xA86549B9 = 162,
            	_0x8AAF337A = 163,
            	_0x13BAA6E7 = 164,
            	_0x5FB9D1F5 = 165,
            	CPED_CONFIG_FLAG_IsInjured = 166,
            	_0x6398A20B = 167,
            	_0xD8072639 = 168,
            	_0xA05B1845 = 169,
            	_0x83F6D220 = 170,
            	_0xD8430331 = 171,
            	_0x4B547520 = 172,
            	_0xE66E1406 = 173,
            	_0x1C4BFE0C = 174,
            	_0x90008BFA = 175,
            	_0x07C7A910 = 176,
            	_0xF15F8191 = 177,
            	_0xCE4E8BE2 = 178,
            	_0x1D46E4F2 = 179,
            	CPED_CONFIG_FLAG_IsInCustody = 180,
            	_0xE4FD9B3A = 181,
            	_0x67AE0812 = 182,
            	CPED_CONFIG_FLAG_IsAgitated = 183,
            	CPED_CONFIG_FLAG_PreventAutoShuffleToDriversSeat = 184,
            	_0x7B2D325E = 185,
            	CPED_CONFIG_FLAG_EnableWeaponBlocking = 186,
            	CPED_CONFIG_FLAG_HasHurtStarted = 187,
            	CPED_CONFIG_FLAG_DisableHurt = 188,
            	CPED_CONFIG_FLAG_PlayerIsWeird = 189,
            	_0x32FC208B = 190,
            	_0x0C296E5A = 191,
            	_0xE63B73EC = 192,
            	_0x04E9CC80 = 193,
            	CPED_CONFIG_FLAG_UsingScenario = 194,
            	CPED_CONFIG_FLAG_VisibleOnScreen = 195,
            	_0xD88C58A1 = 196,
            	_0x5A3DCF43 = 197, // CPED_CONFIG_FLAG_AvoidUnderSide
            	_0xEA02B420 = 198,
            	_0x3F559CFF = 199,
            	_0x8C55D029 = 200,
            	_0x5E6466F6 = 201,
            	_0xEB5AD706 = 202,
            	_0x0EDDDDE7 = 203,
            	_0xA64F7B1D = 204,
            	_0x48532CBA = 205,
            	_0xAA25A9E7 = 206,
            	_0x415B26B9 = 207,
            	CPED_CONFIG_FLAG_DisableExplosionReactions = 208,
            	CPED_CONFIG_FLAG_DodgedPlayer = 209,
            	_0x67405504 = 210,
            	_0x75DDD68C = 211,
            	_0x2AD879B4 = 212,
            	_0x51486F91 = 213,
            	_0x32F79E21 = 214,
            	_0xBF099213 = 215,
            	_0x054AC8E2 = 216,
            	_0x14E495CC = 217,
            	_0x3C7DF9DF = 218,
            	_0x848FFEF2 = 219,
            	CPED_CONFIG_FLAG_DontEnterLeadersVehicle = 220,
            	_0x2618E1CF = 221,
            	_0x84F722FA = 222,
            	_0xD1B87B1F = 223,
            	_0x728AA918 = 224,
            	CPED_CONFIG_FLAG_DisablePotentialToBeWalkedIntoResponse = 225,
            	CPED_CONFIG_FLAG_DisablePedAvoidance = 226,
            	_0x59E91185 = 227,
            	_0x1EA7225F = 228,
            	CPED_CONFIG_FLAG_DisablePanicInVehicle = 229,
            	_0x6DCA7D88 = 230,
            	_0xFC3E572D = 231,
            	_0x08E9F9CF = 232,
            	_0x2D3BA52D = 233,
            	_0xFD2F53EA = 234,
            	_0x31A1B03B = 235,
            	CPED_CONFIG_FLAG_IsHoldingProp = 236,
            	_0x82ED0A66 = 237, // CPED_CONFIG_FLAG_BlocksPathingWhenDead
            	_0xCE57C9A3 = 238,
            	_0x26149198 = 239,
            	_0x1B33B598 = 240,
            	_0x719B6E87 = 241,
            	_0x13E8E8E8 = 242,
            	_0xF29739AE = 243,
            	_0xABEA8A74 = 244,
            	_0xB60EA2BA = 245,
            	_0x536B0950 = 246,
            	_0x0C754ACA = 247,
            	CPED_CONFIG_FLAG_DisableVehicleSeatRandomAnimations = 248,
            	_0x12659168 = 249,
            	_0x1BDF2F04 = 250,
            	_0x7728FAA3 = 251,
            	_0x6A807ED8 = 252,
            	CPED_CONFIG_FLAG_OnStairs = 253,
            	_0xE1A2F73F = 254,
            	_0x5B3697C8 = 255,
            	_0xF1EB20A9 = 256,
            	_0x8B7DF407 = 257,
            	_0x329DCF1A = 258,
            	_0x8D90DD1B = 259,
            	_0xB8A292B7 = 260,
            	_0x8374B087 = 261,
            	_0x2AF558F0 = 262,
            	_0x82251455 = 263,
            	_0x30CF498B = 264,
            	_0xE1CD50AF = 265,
            	_0x72E4AE48 = 266,
            	_0xC2657EA1 = 267,
            	_0x29FF6030 = 268,
            	_0x8248A5EC = 269,
            	CPED_CONFIG_FLAG_OnStairSlope = 270,
            	_0xA0897933 = 271,
            	CPED_CONFIG_FLAG_DontBlipCop = 272,
            	CPED_CONFIG_FLAG_ClimbedShiftedFence = 273,
            	_0xF7823618 = 274,
            	_0xDC305CCE = 275, // CPED_CONFIG_FLAG_KillWhenTrapped
            	CPED_CONFIG_FLAG_EdgeDetected = 276,
            	_0x92B67896 = 277,
            	_0xCAD677C9 = 278,
            	CPED_CONFIG_FLAG_AvoidTearGas = 279,
            	_0x5276AC7B = 280,
            	_0x1032692A = 281,
            	_0xDA23E7F1 = 282,
            	_0x9139724D = 283,
            	_0xA1457461 = 284,
            	_0x4186E095 = 285,
            	_0xAC68E2EB = 286,
            	CPED_CONFIG_FLAG_RagdollingOnBoat = 287,
            	CPED_CONFIG_FLAG_HasBrandishedWeapon = 288,
            	_0x1B9EE8A1 = 289,
            	_0xF3F5758C = 290,
            	_0x2A9307F1 = 291,
            	_0x7403D216 = 292,
            	_0xA06A3C6C = 293,
            	CPED_CONFIG_FLAG_DisableShockingEvents = 294,
            	_0xF8DA25A5 = 295,
            	_0x7EF55802 = 296,
            	_0xB31F1187 = 297,
            	_0x84315402 = 298,
            	_0x0FD69867 = 299,
            	_0xC7829B67 = 300,
            	CPED_CONFIG_FLAG_DisablePedConstraints = 301,
            	_0x6D23CF25 = 302,
            	_0x2ADA871B = 303,
            	_0x47BC8A58 = 304,
            	_0xEB692FA5 = 305,
            	_0x4A133C50 = 306,
            	_0xC58099C3 = 307,
            	_0xF3D76D41 = 308,
            	_0xB0EEE9F2 = 309,
            	CPED_CONFIG_FLAG_IsInCluster = 310,
            	_0x0FA153EF = 311,
            	_0xD73F5CD3 = 312,
            	_0xD4136C22 = 313,
            	_0xE404CA6B = 314,
            	_0xB9597446 = 315,
            	_0xD5C98277 = 316,
            	_0xD5060A9C = 317,
            	_0x3E5F1CBB = 318,
            	_0xD8BE1D54 = 319,
            	_0x0B1F191F = 320,
            	_0xC995167A = 321,
            	CPED_CONFIG_FLAG_HasHighHeels = 322,
            	_0x86B01E54 = 323,
            	_0x3A56FE15 = 324,
            	_0xC03B736C = 325, // CPED_CONFIG_FLAG_SpawnedAtScenario
            	_0xBBF47729 = 326,
            	_0x22B668A8 = 327,
            	_0x2624D4D4 = 328,
            	CPED_CONFIG_FLAG_DisableTalkTo = 329,
            	CPED_CONFIG_FLAG_DontBlip = 330,
            	CPED_CONFIG_FLAG_IsSwitchingWeapon = 331,
            	_0x630F55F3 = 332,
            	_0x150468FD = 333,
            	_0x914EBD6B = 334,
            	_0x79AF3B6D = 335,
            	_0x75C7A632 = 336,
            	_0x52D530E2 = 337,
            	_0xDB2A90E0 = 338,
            	_0x5922763D = 339,
            	_0x12ADB567 = 340,
            	_0x105C8518 = 341,
            	_0x106F703D = 342,
            	_0xED152C3E = 343,
            	_0xA0EFE6A8 = 344,
            	_0xBF348C82 = 345,
            	_0xCDDFE830 = 346,
            	_0x7B59BD9B = 347,
            	_0x0124C788 = 348,
            	CPED_CONFIG_FLAG_EquipJetpack = 349,
            	_0x08D361A5 = 350,
            	_0xE13D1F7C = 351,
            	_0x40E25FB9 = 352,
            	_0x930629D9 = 353,
            	_0xECCF0C7F = 354,
            	_0xB6E9613B = 355,
            	_0x490C0478 = 356,
            	_0xE8865BEA = 357,
            	_0xF3C34A29 = 358,
            	CPED_CONFIG_FLAG_IsDuckingInVehicle = 359,
            	_0xF660E115 = 360,
            	_0xAB0E6DED = 361,
            	CPED_CONFIG_FLAG_HasReserveParachute = 362,
            	CPED_CONFIG_FLAG_UseReserveParachute = 363,
            	_0x5C5D9CD3 = 364,
            	_0x8F7701F3 = 365,
            	_0xBC4436AD = 366,
            	_0xD7E07D37 = 367,
            	_0x03C4FD24 = 368,
            	_0x7675789A = 369,
            	_0xB7288A88 = 370,
            	_0xC06B6291 = 371,
            	_0x95A4A805 = 372,
            	_0xA8E9A042 = 373,
            	CPED_CONFIG_FLAG_NeverLeaveTrain = 374,
            	_0xBAC674B3 = 375,
            	_0x147F1FFB = 376,
            	_0x4376DD79 = 377,
            	_0xCD3DB518 = 378,
            	_0xFE4BA4B6 = 379,
            	_0x5DF03A55 = 380,
            	_0xBCD816CD = 381,
            	_0xCF02DD69 = 382,
            	_0xF73AFA2E = 383,
            	_0x80B9A9D0 = 384,
            	_0xF601F7EE = 385,
            	_0xA91350FC = 386,
            	_0x3AB23B96 = 387,
            	CPED_CONFIG_FLAG_IsClimbingLadder = 388,
            	CPED_CONFIG_FLAG_HasBareFeet = 389,
            	_0xB4B1CD4C = 390,
            	_0x5459AFB8 = 391,
            	_0x54F27667 = 392,
            	_0xC11D3E8F = 393,
            	_0x5419EB3E = 394,
            	_0x82D8DBB4 = 395,
            	_0x33B02D2F = 396,
            	_0xAE66176D = 397,
            	_0xA2692593 = 398,
            	_0x714C7E31 = 399,
            	_0xEC488AC7 = 400,
            	_0xAE398504 = 401,
            	_0xABC58D72 = 402,
            	_0x5E5B9591 = 403,
            	_0x6BA1091E = 404,
            	_0x77840177 = 405,
            	_0x1C7ACAC4 = 406,
            	_0x124420E9 = 407,
            	_0x75A65587 = 408,
            	_0xDFD2D55B = 409,
            	_0xBDD39919 = 410,
            	_0x43DEC267 = 411,
            	_0xE42B7797 = 412,
            	CPED_CONFIG_FLAG_IsHolsteringWeapon = 413,
            	_0x4F8149F5 = 414,
            	_0xDD9ECA7A = 415,
            	_0x9E7EF9D2 = 416,
            	_0x2C6ED942 = 417,
            	CPED_CONFIG_FLAG_IsSwitchingHelmetVisor = 418,
            	_0xA488727D = 419,
            	_0xCFF5F6DE = 420,
            	_0x6D614599 = 421,
            	CPED_CONFIG_FLAG_DisableVehicleCombat = 422,
            	_0xFE401D26 = 423,
            	CPED_CONFIG_FLAG_FallsLikeAircraft = 424,
            	_0x2B42AE82 = 425,
            	_0x7A95734F = 426,
            	_0xDF4D8617 = 427,
            	_0x578F1F14 = 428,
            	CPED_CONFIG_FLAG_DisableStartEngine = 429,
            	CPED_CONFIG_FLAG_IgnoreBeingOnFire = 430,
            	_0x153C9500 = 431,
            	_0xCB7A632E = 432,
            	_0xDE727981 = 433,
            	CPED_CONFIG_FLAG_DisableHomingMissileLockon = 434,
            	_0x12BBB935 = 435,
            	_0xAD0A1277 = 436,
            	_0xEA6AA46A = 437,
            	CPED_CONFIG_FLAG_DisableHelmetArmor = 438,
            	_0xCB7F3A1E = 439,
            	_0x50178878 = 440,
            	_0x051B4F0D = 441,
            	_0x2FC3DECC = 442,
            	_0xC0030B0B = 443,
            	_0xBBDAF1E9 = 444,
            	_0x944FE59C = 445,
            	_0x506FBA39 = 446,
            	_0xDD45FE84 = 447,
            	_0xE698AE75 = 448,
            	_0x199633F8 = 449,
            	CPED_CONFIG_FLAG_PedIsArresting = 450,
            	CPED_CONFIG_FLAG_IsDecoyPed = 451,
            	_0x3A251D83 = 452,
            	_0xA56F6986 = 453,
            	_0x1D19C622 = 454,
            	_0xB68D3EAB = 455,
            	CPED_CONFIG_FLAG_CanBeIncapacitated = 456,
            	_0x4BD5EBAD = 457,
            }
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedDefaultComponentVariation(System.Int32)">
            <summary>
            Sets Ped Default Clothes
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedEyeColor(System.Int32,System.Int32)">
            <summary>
            Used for freemode (online) characters.
            
            Indices:
            
            1.  black
            2.  very light blue/green
            3.  dark blue
            4.  brown
            5.  darker brown
            6.  light brown
            7.  blue
            8.  light blue
            9.  pink
            10. yellow
            11. purple
            12. black
            13. dark green
            14. light brown
            15. yellow/black pattern
            16. light colored spiral pattern
            17. shiny red
            18. shiny half blue/half red
            19. half black/half light blue
            20. white/red perimter
            21. green snake
            22. red snake
            23. dark blue snake
            24. dark yellow
            25. bright yellow
            26. all black
            27. red small pupil
            28. devil blue/black
            29. white small pupil
            30. glossed over
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedFaceFeature(System.Int32,System.Int32,System.Single)">
            <summary>
            Sets the various freemode face features, e.g. nose length, chin shape.
            
            **Indexes (From 0 to 19):**
            
            Parentheses indicate morph scale/direction as in (-1.0 to 1.0)
            
            *   **0**: Nose Width (Thin/Wide)
            *   **1**: Nose Peak (Up/Down)
            *   **2**: Nose Length (Long/Short)
            *   **3**: Nose Bone Curveness (Crooked/Curved)
            *   **4**: Nose Tip (Up/Down)
            *   **5**: Nose Bone Twist (Left/Right)
            *   **6**: Eyebrow (Up/Down)
            *   **7**: Eyebrow (In/Out)
            *   **8**: Cheek Bones (Up/Down)
            *   **9**: Cheek Sideways Bone Size (In/Out)
            *   **10**: Cheek Bones Width (Puffed/Gaunt)
            *   **11**: Eye Opening (Both) (Wide/Squinted)
            *   **12**: Lip Thickness (Both) (Fat/Thin)
            *   **13**: Jaw Bone Width (Narrow/Wide)
            *   **14**: Jaw Bone Shape (Round/Square)
            *   **15**: Chin Bone (Up/Down)
            *   **16**: Chin Bone Length (In/Out or Backward/Forward)
            *   **17**: Chin Bone Shape (Pointed/Square)
            *   **18**: Chin Hole (Chin Bum)
            *   **19**: Neck Thickness (Thin/Thick)
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
            <param name="ped">
            The ped entity
            </param>
            <param name="index">
            An integer ranging from 0 to 19
            </param>
            <param name="scale">
            A float ranging from -1.0 to 1.0
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedHairColor(System.Int32,System.Int32,System.Int32)">
            <summary>
            Used for freemode (online) characters.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedHeadBlendData(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Single,System.Single,System.Single,System.Boolean)">
            <summary>
            For more info please refer to [this](https://gtaforums.com/topic/858970-all-gtao-face-ids-pedset-ped-head-blend-data-explained) topic.
            
            **Other information:**
            
            IDs start at zero and go Male Non-DLC, Female Non-DLC, Male DLC, and Female DLC.&lt;/br&gt;
            
            This native function is often called prior to calling natives such as:
            
            *   [`SetPedHairColor`](#\_0xBB43F090)
            *   [`SetPedHeadOverlayColor`](#\_0x78935A27)
            *   [`SetPedHeadOverlay`](#\_0xD28DBA90)
            *   [`SetPedFaceFeature`](#\_0x6C8D4458)
            </summary>
            <param name="ped">
            The ped entity
            </param>
            <param name="shapeFirstID">
            Controls the shape of the first ped's face
            </param>
            <param name="shapeSecondID">
            Controls the shape of the second ped's face
            </param>
            <param name="shapeThirdID">
            Controls the shape of the third ped's face
            </param>
            <param name="skinFirstID">
            Controls the first id's skin tone
            </param>
            <param name="skinSecondID">
            Controls the second id's skin tone
            </param>
            <param name="skinThirdID">
            Controls the third id's skin tone
            </param>
            <param name="shapeMix">
            0.0 - 1.0 Of whose characteristics to take Mother -&gt; Father (shapeFirstID and shapeSecondID)
            </param>
            <param name="skinMix">
            0.0 - 1.0 Of whose characteristics to take Mother -&gt; Father (skinFirstID and skinSecondID)
            </param>
            <param name="thirdMix">
            Overrides the others in favor of the third IDs.
            </param>
            <param name="isParent">
            IsParent is set for "children" of the player character's grandparents during old-gen character creation. It has unknown effect otherwise.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedHeadOverlay(System.Int32,System.Int32,System.Int32,System.Single)">
            <summary>
            ```
            OverlayID ranges from 0 to 12, index from 0 to _GET_NUM_OVERLAY_VALUES(overlayID)-1, and opacity from 0.0 to 1.0.   
            overlayID       Part                  Index, to disable  
            0               Blemishes             0 - 23, 255  
            1               Facial Hair           0 - 28, 255  
            2               Eyebrows              0 - 33, 255  
            3               Ageing                0 - 14, 255  
            4               Makeup                0 - 74, 255  
            5               Blush                 0 - 6, 255  
            6               Complexion            0 - 11, 255  
            7               Sun Damage            0 - 10, 255  
            8               Lipstick              0 - 9, 255  
            9               Moles/Freckles        0 - 17, 255  
            10              Chest Hair            0 - 16, 255  
            11              Body Blemishes        0 - 11, 255  
            12              Add Body Blemishes    0 - 1, 255  
            ```
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
            <param name="ped">
            The ped entity
            </param>
            <param name="overlayID">
            The overlay id displayed up above.
            </param>
            <param name="index">
            An integer representing the index (from 0 to `_GET_NUM_OVERLAY_VALUES(overlayID)-1`)
            </param>
            <param name="opacity">
            A float ranging from 0.0 to 1.0
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedHeadOverlayColor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            ```
            Used for freemode (online) characters. 
            Called after SET_PED_HEAD_OVERLAY().  
            ```
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
            <param name="ped">
            The ped entity
            </param>
            <param name="overlayID">
            An integer representing the overlay id
            </param>
            <param name="colorType">
            1 for eyebrows, beards, and chest hair; 2 for blush and lipstick; and 0 otherwise, though not called in those cases.
            </param>
            <param name="colorID">
            An integer representing the primary color id
            </param>
            <param name="secondColorID">
            An integer representing the secondary color id
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedIntoVehicle(System.Int32,System.Int32,System.Int32)">
            <summary>
            SET_PED_INTO_VEHICLE
            </summary>
            <param name="seatIndex">
            See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669). -2 for the first available seat.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedPropIndex(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            This native is used to set prop variation on a ped. Components, drawables and textures IDs are related to the ped model.
            
            ### MP Freemode list of props
            
            **0**: Hats
            **1**: Glasses
            **2**: Ears
            **6**: Watches
            **7**: Bracelets
            
            List of Prop IDs
            
            ```cpp
            // Props
            enum eAnchorPoints
            {
                ANCHOR_HEAD = 0, // "p_head"
                ANCHOR_EYES = 1, // "p_eyes"
                ANCHOR_EARS = 2, // "p_ears"
                ANCHOR_MOUTH = 3, // "p_mouth"
                ANCHOR_LEFT_HAND = 4, // "p_lhand"
                ANCHOR_RIGHT_HAND = 5, // "p_rhand"
                ANCHOR_LEFT_WRIST = 6, // "p_lwrist"
                ANCHOR_RIGHT_WRIST = 7, // "p_rwrist"
                ANCHOR_HIP = 8, // "p_lhip"
                ANCHOR_LEFT_FOOT = 9, // "p_lfoot"
                ANCHOR_RIGHT_FOOT = 10, // "p_rfoot"
                ANCHOR_PH_L_HAND = 11, // "ph_lhand"
                ANCHOR_PH_R_HAND = 12, // "ph_rhand"
                NUM_ANCHORS = 13,
            };
            ```
            </summary>
            <param name="ped">
            The ped handle.
            </param>
            <param name="componentId">
            The component that you want to set. Refer to [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80).
            </param>
            <param name="drawableId">
            The drawable id that is going to be set. Refer to [GET_NUMBER_OF_PED_PROP_DRAWABLE_VARIATIONS](#\_0x5FAF9754E789FB47).
            </param>
            <param name="textureId">
            The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_PROP_TEXTURE_VARIATIONS](#\_0xA6E7F1CEB523E171).
            </param>
            <param name="attach">
            Attached or not.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedRandomComponentVariation(System.Int32,System.Int32)">
            <summary>
            p1 is always 0 in R* scripts; and a quick disassembly seems to indicate that p1 is unused.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedRandomProps(System.Int32)">
            <summary>
            SET_PED_RANDOM_PROPS
            </summary>
            <param name="ped">
            The ped handle.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedResetFlag(System.Int32,System.Int32,System.Boolean)">
            <summary>
            PED::SET_PED_RESET_FLAG(PLAYER::PLAYER_PED_ID(), 240, 1);
            Known values:
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedToRagdoll(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            p4/p5: Unusued in TU27
            
            ### Ragdoll Types
            
            **0**: CTaskNMRelax
            **1**: CTaskNMScriptControl: Hardcoded not to work in networked environments.
            **Else**: CTaskNMBalance
            </summary>
            <param name="time1">
            Time(ms) Ped is in ragdoll mode; only applies to ragdoll types 0 and not 1.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPedToRagdollWithFall(System.Int32,System.Int32,System.Int32,System.Int32,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Return variable is never used in R*'s scripts.  
            Not sure what p2 does. It seems like it would be a time judging by it's usage in R*'s scripts, but didn't seem to affect anything in my testings.  
            x, y, and z are coordinates, most likely to where the ped will fall.  
            p7 is probably the force of the fall, but untested, so I left the variable name the same.  
            p8 to p13 are always 0f in R*'s scripts.  
            (Simplified) Example of the usage of the function from R*'s scripts:  
            ped::set_ped_to_ragdoll_with_fall(ped, 1500, 2000, 1, -entity::get_entity_forward_vector(ped), 1f, 0f, 0f, 0f, 0f, 0f, 0f);
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerControl(CitizenFX.Core.CString,System.Boolean,System.Int32)">
            <summary>
            Flags:
            SPC_AMBIENT_SCRIPT = (1 &lt;&lt; 1),
            SPC_CLEAR_TASKS = (1 &lt;&lt; 2),
            SPC_REMOVE_FIRES = (1 &lt;&lt; 3),
            SPC_REMOVE_EXPLOSIONS = (1 &lt;&lt; 4),
            SPC_REMOVE_PROJECTILES = (1 &lt;&lt; 5),
            SPC_DEACTIVATE_GADGETS = (1 &lt;&lt; 6),
            SPC_REENABLE_CONTROL_ON_DEATH = (1 &lt;&lt; 7),
            SPC_LEAVE_CAMERA_CONTROL_ON = (1 &lt;&lt; 8),
            SPC_ALLOW_PLAYER_DAMAGE = (1 &lt;&lt; 9),
            SPC_DONT_STOP_OTHER_CARS_AROUND_PLAYER = (1 &lt;&lt; 10),
            SPC_PREVENT_EVERYBODY_BACKOFF = (1 &lt;&lt; 11),
            SPC_ALLOW_PAD_SHAKE = (1 &lt;&lt; 12)
            See: https://alloc8or.re/gta5/doc/enums/eSetPlayerControlFlag.txt
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerCullingRadius(CitizenFX.Core.CString,System.Single)">
            <summary>
            Sets the culling radius for the specified player.
            Set to `0.0` to reset.
            
            **WARNING**: Culling natives are deprecated and have known, [unfixable issues](https://forum.cfx.re/t/issue-with-culling-radius-and-server-side-entities/4900677/4)
            </summary>
            <param name="playerSrc">
            The player to set the culling radius for.
            </param>
            <param name="radius">
            The radius.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerInvincible(CitizenFX.Core.CString,System.Boolean)">
            <summary>
            Simply sets you as invincible (Health will not deplete).  
            Use 0x733A643B5B0C53C1 instead if you want Ragdoll enabled, which is equal to:  
            *(DWORD *)(playerPedAddress + 0x188) |= (1 &lt;&lt; 9);
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerModel(CitizenFX.Core.CString,System.UInt32)">
            <summary>
            Set the model for a specific Player. Note that this will destroy the current Ped for the Player and create a new one, any reference to the old ped will be invalid after calling this.
            
            As per usual, make sure to request the model first and wait until it has loaded.
            </summary>
            <param name="player">
            The player to set the model for
            </param>
            <param name="model">
            The model to use
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerRoutingBucket(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Sets the routing bucket for the specified player.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
            <param name="playerSrc">
            The player to set the routing bucket for.
            </param>
            <param name="bucket">
            The bucket ID.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetPlayerWantedLevel(CitizenFX.Core.CString,System.Int32,System.Boolean)">
            <summary>
            SET_PLAYER_WANTED_LEVEL
            </summary>
            <param name="player">
            the target player
            </param>
            <param name="wantedLevel">
            the wanted level 1-5
            </param>
            <param name="delayedResponse">
            false = 0-10sec police spawn response time, true = 10-20sec police spawn response time
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvp(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            A setter for [GET_RESOURCE_KVP_STRING](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvpFloat(CitizenFX.Core.CString,System.Single)">
            <summary>
            A setter for [GET_RESOURCE_KVP_FLOAT](#\_0x35BDCEEA).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvpFloatNoSync(CitizenFX.Core.CString,System.Single)">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvpInt(CitizenFX.Core.CString,System.Int32)">
            <summary>
            A setter for [GET_RESOURCE_KVP_INT](#\_0x557B586A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvpIntNoSync(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetResourceKvpNoSync(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP](#\_0x21C7A35B) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetRoutingBucketEntityLockdownMode(System.Int32,CitizenFX.Core.CString)">
            <summary>
            Sets the entity lockdown mode for a specific routing bucket.
            
            Lockdown modes are:
            
            | Mode       | Meaning                                                    |
            | ---------- | ---------------------------------------------------------- |
            | `strict`   | No entities can be created by clients at all.              |
            | `relaxed`  | Only script-owned entities created by clients are blocked. |
            | `inactive` | Clients can create any entity they want.                   |
            </summary>
            <param name="bucketId">
            The routing bucket ID to adjust.
            </param>
            <param name="mode">
            One of aforementioned modes.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetRoutingBucketPopulationEnabled(System.Int32,System.Boolean)">
            <summary>
            Sets whether or not the specified routing bucket has automatically-created population enabled.
            </summary>
            <param name="bucketId">
            The routing bucket ID to adjust.
            </param>
            <param name="mode">
            `true` to enable population, `false` to disable population.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetStateBagValue(CitizenFX.Core.CString,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32,System.Boolean)">
            <summary>
            Internal function for setting a state bag value.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleAlarm(System.Int32,System.Boolean)">
            <summary>
            SET_VEHICLE_ALARM
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleBodyHealth(System.Int32,System.Single)">
            <summary>
            p2 often set to 1000.0 in the decompiled scripts.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleColourCombination(System.Int32,System.Int32)">
            <summary>
            Sets the selected vehicle's colors to their default value (specific variant specified using the colorCombination parameter).
            
            Range of possible values for colorCombination is currently unknown, I couldn't find where these values are stored either (Disquse's guess was vehicles.meta but I haven't seen it in there.)
            </summary>
            <param name="vehicle">
            The vehicle to modify.
            </param>
            <param name="colorCombination">
            One of the default color values of the vehicle.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleColours(System.Int32,System.Int32,System.Int32)">
            <summary>
            colorPrimary &amp; colorSecondary are the paint indexes for the vehicle.
            
            For a list of valid paint indexes, view: pastebin.com/pwHci0xK
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleCustomPrimaryColour(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleCustomSecondaryColour(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleDirtLevel(System.Int32,System.Single)">
            <summary>
            Sets the dirt level of the passed vehicle.
            </summary>
            <param name="vehicle">
            The vehicle to set.
            </param>
            <param name="dirtLevel">
            A number between 0.0 and 15.0 representing the vehicles dirt level.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleDoorBroken(System.Int32,System.Int32,System.Boolean)">
            <summary>
            See eDoorId declared in [`SET_VEHICLE_DOOR_SHUT`](#\_0x93D9BD300D7789E5)
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleDoorsLocked(System.Int32,System.Int32)">
            <summary>
            // Source GTA VC miss2 leak, matching constants for 0/2/4, testing
            // They use 10 in am_mp_property_int, don't know what it does atm.
            enum eCarLock {
                CARLOCK_NONE = 0,
                CARLOCK_UNLOCKED = 1,
                CARLOCK_LOCKED = 2,
                CARLOCK_LOCKOUT_PLAYER_ONLY = 3,
                CARLOCK_LOCKED_PLAYER_INSIDE = 4,
                CARLOCK_LOCKED_INITIALLY = 5,
                CARLOCK_FORCE_SHUT_DOORS = 6,
                CARLOCK_LOCKED_BUT_CAN_BE_DAMAGED = 7
            };
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.SetVehicleNumberPlateText(System.Int32,CitizenFX.Core.CString)">
            <summary>
            SET_VEHICLE_NUMBER_PLATE_TEXT
            </summary>
            <param name="vehicle">
            The vehicle to set the plate for
            </param>
            <param name="plateText">
            The text to set the plate to, 8 chars maximum
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.StartFindKvp(CitizenFX.Core.CString)">
            <summary>
            START_FIND_KVP
            </summary>
            <param name="prefix">
            A prefix match
            </param>
            <returns>
            A KVP find handle to use with [FIND_KVP](#\_0xBD7BEBC5) and close with [END_FIND_KVP](#\_0xB3210203)
            </returns>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.StartResource(CitizenFX.Core.CString)">
            <summary>
            START_RESOURCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.StopResource(CitizenFX.Core.CString)">
            <summary>
            STOP_RESOURCE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskCombatPed(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Makes the specified ped attack the target ped.  
            p2 should be 0  
            p3 should be 16
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskDriveBy(System.Int32,System.Int32,System.Int32,System.Single,System.Single,System.Single,System.Single,System.Int32,System.Boolean,System.UInt32)">
            <summary>
            Example:
            TASK::TASK_DRIVE_BY(l_467[1/*22*/], PLAYER::PLAYER_PED_ID(), 0, 0.0, 0.0, 2.0, 300.0, 100, 0, ${firing_pattern_burst_fire_driveby});
            Needs working example. Doesn't seem to do anything.
            I marked p2 as targetVehicle as all these shooting related tasks seem to have that in common.
            I marked p6 as distanceToShoot as if you think of GTA's Logic with the native SET_VEHICLE_SHOOT natives, it won't shoot till it gets within a certain distance of the target.
            I marked p7 as pedAccuracy as it seems it's mostly 100 (Completely Accurate), 75, 90, etc. Although this could be the ammo count within the gun, but I highly doubt it. I will change this comment once I find out if it's ammo count or not.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskEnterVehicle(System.Int32,System.Int32,System.Int32,System.Int32,System.Single,System.Int32,System.Int64)">
            <summary>
            speed 1.0 = walk, 2.0 = run  
            p5 1 = normal, 3 = teleport to vehicle, 8 = normal/carjack ped from seat, 16 = teleport directly into vehicle  
            p6 is always 0
            </summary>
            <param name="seatIndex">
            See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskEveryoneLeaveVehicle(System.Int32)">
            <summary>
            TASK_EVERYONE_LEAVE_VEHICLE
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskGoStraightToCoord(System.Int32,System.Single,System.Single,System.Single,System.Single,System.Int32,System.Single,System.Single)">
            <summary>
            TASK_GO_STRAIGHT_TO_COORD
            </summary>
            <param name="ped">
            The ped handle.
            </param>
            <param name="x">
            The x coordinate.
            </param>
            <param name="y">
            The y coordinate.
            </param>
            <param name="z">
            The z coordinate.
            </param>
            <param name="speed">
            The ped movement speed.
            </param>
            <param name="timeout">
            \-1 , other values appear to break the ped movement.
            </param>
            <param name="targetHeading">
            The heading you want the ped to be on x,y,z coord.
            </param>
            <param name="distanceToSlide">
            The distance from x,y,z where the ped will start sliding.
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskGoToCoordAnyMeans(System.Int32,System.Single,System.Single,System.Single,System.Single,System.Int64,System.Boolean,System.Int32,System.Single)">
            <summary>
            example from fm_mission_controller
            TASK::TASK_GO_TO_COORD_ANY_MEANS(l_649, sub_f7e86(-1, 0), 1.0, 0, 0, 786603, 0xbf800000);
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskGoToEntity(System.Int32,System.Int32,System.Int32,System.Single,System.Single,System.Single,System.Int32)">
            <summary>
            The entity will move towards the target until time is over (duration) or get in target's range (distance). p5 and p6 are unknown, but you could leave p5 = 1073741824 or 100 or even 0 (didn't see any difference but on the decompiled scripts, they use 1073741824 mostly) and p6 = 0
            Note: I've only tested it on entity -&gt; ped and target -&gt; vehicle. It could work differently on other entities, didn't try it yet.
            Example: TASK::TASK_GO_TO_ENTITY(pedHandle, vehicleHandle, 5000, 4.0, 100, 1073741824, 0)
            Ped will run towards the vehicle for 5 seconds and stop when time is over or when he gets 4 meters(?) around the vehicle (with duration = -1, the task duration will be ignored).
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskHandsUp(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            In the scripts, p3 was always -1.  
            p3 seems to be duration or timeout of turn animation.  
            Also facingPed can be 0 or -1 so ped will just raise hands up.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskLeaveAnyVehicle(System.Int32,System.Int32,System.Int32)">
            <summary>
            Flags are the same flags used in [`TASK_LEAVE_VEHICLE`](#\_0xD3DBCE61A490BE02)
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskLeaveVehicle(System.Int32,System.Int32,System.Int32)">
            <summary>
            Flags from decompiled scripts:  
            0 = normal exit and closes door.  
            1 = normal exit and closes door.  
            16 = teleports outside, door kept closed.  (This flag does not seem to work for the front seats in buses, NPCs continue to exit normally)
            64 = normal exit and closes door, maybe a bit slower animation than 0.  
            256 = normal exit but does not close the door.  
            4160 = ped is throwing himself out, even when the vehicle is still.  
            262144 = ped moves to passenger seat first, then exits normally  
            Others to be tried out: 320, 512, 131072.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskPlayAnim(System.Int32,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Single,System.Single,System.Int32,System.Int32,System.Single,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            [Animations list](https://alexguirre.github.io/animations-list/)
            
            ```
            float blendInSpeed &gt; normal speed is 8.0f
            ----------------------  
            float blendOutSpeed &gt; normal speed is 8.0f
            ----------------------  
            int duration: time in millisecond  
            ----------------------  
            -1 _ _ _ _ _ _ _&gt; Default (see flag)  
            0 _ _ _ _ _ _ _ &gt; Not play at all  
            Small value _ _ &gt; Slow down animation speed  
            Other _ _ _ _ _ &gt; freeze player control until specific time (ms) has   
            _ _ _ _ _ _ _ _ _ passed. (No effect if flag is set to be   
            _ _ _ _ _ _ _ _ _ controllable.)  
            int flag:  
            ----------------------  
            enum eAnimationFlags  
            {  
             ANIM_FLAG_NORMAL = 0,  
               ANIM_FLAG_REPEAT = 1,  
               ANIM_FLAG_STOP_LAST_FRAME = 2,  
               ANIM_FLAG_UPPERBODY = 16,  
               ANIM_FLAG_ENABLE_PLAYER_CONTROL = 32,  
               ANIM_FLAG_CANCELABLE = 120,  
            };  
            Odd number : loop infinitely  
            Even number : Freeze at last frame  
            Multiple of 4: Freeze at last frame but controllable  
            01 to 15 &gt; Full body  
            10 to 31 &gt; Upper body  
            32 to 47 &gt; Full body &gt; Controllable  
            48 to 63 &gt; Upper body &gt; Controllable  
            ...  
            001 to 255 &gt; Normal  
            256 to 511 &gt; Garbled  
            ...  
            playbackRate:  
            values are between 0.0 and 1.0  
            lockX:    
            0 in most cases 1 for rcmepsilonism8 and rcmpaparazzo_3  
            &gt; 1 for mini@sprunk  
            lockY:  
            0 in most cases   
            1 for missfam5_yoga, missfra1mcs_2_crew_react  
            lockZ:   
                0 for single player   
                Can be 1 but only for MP  
            ```
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskPlayAnimAdvanced(System.Int32,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Int32,System.Int64,System.Single,System.Int64,System.Int64)">
            <summary>
            It's similar to the one above, except the first 6 floats let you specify the initial position and rotation of the task. (Ped gets teleported to the position).
            
            [Animations list](https://alexguirre.github.io/animations-list/)
            </summary>
            <param name="ped">
            The target ped
            </param>
            <param name="animDict">
            Name of the animation dictionary
            </param>
            <param name="animName">
            Name of the animation
            </param>
            <param name="posX">
            Initial X position of the task
            </param>
            <param name="posY">
            Initial Y position of the task
            </param>
            <param name="posZ">
            Initial Z position of the task
            </param>
            <param name="rotX">
            Initial X rotation of the task, doesn't seem to have any effect
            </param>
            <param name="rotY">
            Initial Y rotation of the task, doesn't seem to have any effect
            </param>
            <param name="rotZ">
            Initial Z rotation of the task
            </param>
            <param name="animEnterSpeed">
            Adjust character speed to fully enter animation
            </param>
            <param name="animExitSpeed">
            Adjust character speed to fully exit animation (useless `ClearPedTasksImmediately()` is called)
            </param>
            <param name="duration">
            Time in milliseconds
            </param>
            <param name="animTime">
            Value between 0.0 and 1.0, lets you start an animation from the given point
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskReactAndFleePed(System.Int32,System.Int32)">
            <summary>
            TASK_REACT_AND_FLEE_PED
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskShootAtCoord(System.Int32,System.Single,System.Single,System.Single,System.Int32,System.UInt32)">
            <summary>
            Firing Pattern Hash Information: https://pastebin.com/Px036isB
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskShootAtEntity(System.Int32,System.Int32,System.Int32,System.UInt32)">
            <summary>
            //this part of the code is to determine at which entity the player is aiming, for example if you want to create a mod where you give orders to peds
            Entity aimedentity;
            Player player = PLAYER::PLAYER_ID();
            PLAYER::_GET_AIMED_ENTITY(player, &amp;aimedentity);
            //bg is an array of peds
            TASK::TASK_SHOOT_AT_ENTITY(bg[i], aimedentity, 5000, MISC::GET_HASH_KEY("FIRING_PATTERN_FULL_AUTO"));
            in practical usage, getting the entity the player is aiming at and then task the peds to shoot at the entity, at a button press event would be better.
            Firing Pattern Hash Information: https://pastebin.com/Px036isB
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TaskWarpPedIntoVehicle(System.Int32,System.Int32,System.Int32)">
            <summary>
            TASK_WARP_PED_INTO_VEHICLE
            </summary>
            <param name="seatIndex">
            See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).
            </param>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TempBanPlayer(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            TEMP_BAN_PLAYER
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TriggerClientEventInternal(CitizenFX.Core.CString,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32)">
            <summary>
            The backing function for TriggerClientEvent.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TriggerEventInternal(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32)">
            <summary>
            The backing function for TriggerEvent.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.TriggerLatentClientEventInternal(CitizenFX.Core.CString,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32,System.Int32)">
            <summary>
            The backing function for TriggerLatentClientEvent.
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.VerifyPasswordHash(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            VERIFY_PASSWORD_HASH
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Native.Natives.WasEventCanceled">
            <summary>
            Returns whether or not the currently executing event was canceled.
            </summary>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash._ADD_BLIP_FOR_AREA">
            <summary>
            Adds a rectangular blip for the specified coordinates/area.
            
            It is recommended to use [SET_BLIP_ROTATION](#\_0xF87683CDF73C3F6E) and [SET_BLIP_COLOUR](#\_0x03D7FB09E75D6B7E) to make the blip not rotate along with the camera.
            
            By default, the blip will show as a *regular* blip with the specified color/sprite if it is outside of the minimap view.
            
            Example image:
            ![minimap](https://i.imgur.com/qLbXWcQ.png)
            ![big map](https://i.imgur.com/0j7O7Rh.png)
            
            (Native name is *likely* to actually be ADD_BLIP_FOR_AREA, but due to the usual reasons this can't be confirmed)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_BLIP_FOR_COORD">
            <summary>
            Creates a blip for the specified coordinates. You can use `SET_BLIP_` natives to change the blip.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_BLIP_FOR_ENTITY">
            <summary>
            Create a blip that by default is red (enemy), you can use [SET_BLIP_AS_FRIENDLY](#\_0xC6F43D0E) to make it blue (friend).\
            Can be used for objects, vehicles and peds.
            
            Example of enemy:
            ![enemy](https://i.imgur.com/fl78svv.png)
            Example of friend:
            ![friend](https://i.imgur.com/Q16ho5d.png)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_BLIP_FOR_RADIUS">
            <summary>
            Create a blip with a radius for the specified coordinates (it doesnt create the blip sprite, so you need to use [AddBlipCoords](#\_0xC6F43D0E))
            
            Example image:
            ![example](https://i.imgur.com/9hQl3DB.png)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_PED_DECORATION_FROM_HASHES">
            <summary>
            Applies an Item from a PedDecorationCollection to a ped. These include tattoos and shirt decals.
            collection - PedDecorationCollection filename hash
            overlay - Item name hash
            Example:
            Entry inside "mpbeach_overlays.xml" -
            &lt;Item&gt;
              &lt;uvPos x="0.500000" y="0.500000" /&gt;
              &lt;scale x="0.600000" y="0.500000" /&gt;
              &lt;rotation value="0.000000" /&gt;
              &lt;nameHash&gt;FM_Hair_Fuzz&lt;/nameHash&gt;
              &lt;txdHash&gt;mp_hair_fuzz&lt;/txdHash&gt;
              &lt;txtHash&gt;mp_hair_fuzz&lt;/txtHash&gt;
              &lt;zone&gt;ZONE_HEAD&lt;/zone&gt;
              &lt;type&gt;TYPE_TATTOO&lt;/type&gt;
              &lt;faction&gt;FM&lt;/faction&gt;
              &lt;garment&gt;All&lt;/garment&gt;
              &lt;gender&gt;GENDER_DONTCARE&lt;/gender&gt;
              &lt;award /&gt;
              &lt;awardLevel /&gt;
            &lt;/Item&gt;
            Code:
            PED::_0x5F5D1665E352A839(PLAYER::PLAYER_PED_ID(), MISC::GET_HASH_KEY("mpbeach_overlays"), MISC::GET_HASH_KEY("fm_hair_fuzz"))
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ADD_STATE_BAG_CHANGE_HANDLER">
            <summary>
            Adds a handler for changes to a state bag.
            
            The function called expects to match the following signature:
            
            ```ts
            function StateBagChangeHandler(bagName: string, key: string, value: any, reserved: number, replicated: boolean);
            ```
            
            *   **bagName**: The internal bag ID for the state bag which changed. This is usually `player:Source`, `entity:NetID`
                or `localEntity:Handle`.
            *   **key**: The changed key.
            *   **value**: The new value stored at key. The old value is still stored in the state bag at the time this callback executes.
            *   **reserved**: Currently unused.
            *   **replicated**: Whether the set is meant to be replicated.
            
            At this time, the change handler can't opt to reject changes.
            
            If bagName refers to an entity, use [GET_ENTITY_FROM_STATE_BAG_NAME](?\_0x4BDF1868) to get the entity handle
            If bagName refers to a player, use [GET_PLAYER_FROM_STATE_BAG_NAME](?\_0xA56135E0) to get the player handle
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.APPLY_FORCE_TO_ENTITY">
            <summary>
            Applies a force to the specified entity.
            
            **List of force types (p1)**:
            
            ```
            public enum ForceType
            {
                MinForce = 0,
                MaxForceRot = 1,
                MinForce2 = 2,
                MaxForceRot2 = 3,
                ForceNoRot = 4,
                ForceRotPlusForce = 5
            }
            ```
            
            Research/documentation on the gtaforums can be found [here](https://gtaforums.com/topic/885669-precisely-define-object-physics/) and [here](https://gtaforums.com/topic/887362-apply-forces-and-momentums-to-entityobject/).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CAN_PLAYER_START_COMMERCE_SESSION">
            <summary>
            Returns whether or not the specified player has enough information to start a commerce session for.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CANCEL_EVENT">
            <summary>
            Cancels the currently executing event.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CLEAR_PED_PROP">
            <summary>
            CLEAR_PED_PROP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CLEAR_PED_SECONDARY_TASK">
            <summary>
            CLEAR_PED_SECONDARY_TASK
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CLEAR_PED_TASKS">
            <summary>
            Clear a ped's tasks. Stop animations and other tasks created by scripts.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CLEAR_PED_TASKS_IMMEDIATELY">
            <summary>
            Immediately stops the pedestrian from whatever it's doing. The difference between this and [CLEAR_PED_TASKS](#\_0xE1EF3C1216AFF2CD) is that this one teleports the ped but does not change the position of the ped.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CLEAR_PLAYER_WANTED_LEVEL">
            <summary>
            This executes at the same as speed as PLAYER::SET_PLAYER_WANTED_LEVEL(player, 0, false);  
            PLAYER::GET_PLAYER_WANTED_LEVEL(player); executes in less than half the time. Which means that it's worth first checking if the wanted level needs to be cleared before clearing. However, this is mostly about good code practice and can important in other situations. The difference in time in this example is negligible.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_OBJECT">
            <summary>
            Creates an object (prop) with the specified model at the specified position, offset on the Z axis by the radius of the object's model.
            This object will initially be owned by the creating script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_OBJECT_NO_OFFSET">
            <summary>
            Creates an object (prop) with the specified model centered at the specified position.
            This object will initially be owned by the creating script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_PED">
            <summary>
            Creates a ped (biped character, pedestrian, actor) with the specified model at the specified position and heading.
            This ped will initially be owned by the creating script as a mission entity, and the model should be loaded already
            (e.g. using REQUEST_MODEL).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_PED_INSIDE_VEHICLE">
            <summary>
            CREATE_PED_INSIDE_VEHICLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_VEHICLE">
            <summary>
            Creates a vehicle with the specified model at the specified position. This vehicle will initially be owned by the creating
            script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).
            
            ```
            NativeDB Added Parameter 8: BOOL p7
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.CREATE_VEHICLE_SERVER_SETTER">
            <summary>
            Equivalent to CREATE_VEHICLE, but it uses 'server setter' logic (like the former CREATE_AUTOMOBILE) as a workaround for
            reliability concerns regarding entity creation RPC.
            
            Unlike CREATE_AUTOMOBILE, this supports other vehicle types as well.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DELETE_ENTITY">
            <summary>
            Deletes the specified entity.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DELETE_FUNCTION_REFERENCE">
            <summary>
            DELETE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DELETE_RESOURCE_KVP">
            <summary>
            DELETE_RESOURCE_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DELETE_RESOURCE_KVP_NO_SYNC">
            <summary>
            Nonsynchronous [DELETE_RESOURCE_KVP](#\_0x7389B5DF) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_BOAT_SINK_WHEN_WRECKED">
            <summary>
            DOES_BOAT_SINK_WHEN_WRECKED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_ENTITY_EXIST">
            <summary>
            DOES_ENTITY_EXIST
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_PLAYER_EXIST">
            <summary>
            Returns whether or not the player exists
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_PLAYER_OWN_SKU">
            <summary>
            Requests whether or not the player owns the specified SKU.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DOES_PLAYER_OWN_SKU_EXT">
            <summary>
            Requests whether or not the player owns the specified package.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DROP_PLAYER">
            <summary>
            DROP_PLAYER
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.DUPLICATE_FUNCTION_REFERENCE">
            <summary>
            DUPLICATE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ENABLE_ENHANCED_HOST_SUPPORT">
            <summary>
            ENABLE_ENHANCED_HOST_SUPPORT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.END_FIND_KVP">
            <summary>
            END_FIND_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.ENSURE_ENTITY_STATE_BAG">
            <summary>
            Internal function for ensuring an entity has a state bag.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.EXECUTE_COMMAND">
            <summary>
            EXECUTE_COMMAND
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.FIND_KVP">
            <summary>
            FIND_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.FLAG_SERVER_AS_PRIVATE">
            <summary>
            FLAG_SERVER_AS_PRIVATE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.FLUSH_RESOURCE_KVP">
            <summary>
            Nonsynchronous operations will not wait for a disk/filesystem flush before returning from a write or delete call. They will be much faster than their synchronous counterparts (e.g., bulk operations), however, a system crash may lose the data to some recent operations.
            
            This native ensures all `_NO_SYNC` operations are synchronized with the disk/filesystem.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.FREEZE_ENTITY_POSITION">
            <summary>
            Freezes or unfreezes an entity preventing its coordinates to change by the player if set to `true`. You can still change the entity position using SET_ENTITY_COORDS.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_AIR_DRAG_MULTIPLIER_FOR_PLAYERS_VEHICLE">
            <summary>
            GET_AIR_DRAG_MULTIPLIER_FOR_PLAYERS_VEHICLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ALL_OBJECTS">
            <summary>
            Returns all object handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ALL_PEDS">
            <summary>
            Returns all peds handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ALL_VEHICLES">
            <summary>
            Returns all vehicle handles known to the server.
            The data returned adheres to the following layout:
            
            ```
            [127, 42, 13, 37]
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CONSOLE_BUFFER">
            <summary>
            Returns the current console output buffer.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CONVAR">
            <summary>
            Can be used to get a console variable of type `char*`, for example a string.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CONVAR_INT">
            <summary>
            Can be used to get a console variable casted back to `int` (an integer value).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_CURRENT_RESOURCE_NAME">
            <summary>
            Returns the name of the currently executing resource.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_ATTACHED_TO">
            <summary>
            Gets the entity that this entity is attached to.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_COLLISION_DISABLED">
            <summary>
            GET_ENTITY_COLLISION_DISABLED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_COORDS">
            <summary>
            Gets the current coordinates for a specified entity. This native is used server side when using OneSync.
            
            See [GET_ENTITY_COORDS](#\_0x3FEF770D40960D5A) for client side.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_FROM_STATE_BAG_NAME">
            <summary>
            Returns the entity handle for the specified state bag name. For use with [ADD_STATE_BAG_CHANGE_HANDLER](?\_0x5BA35AAF).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_HEADING">
            <summary>
            GET_ENTITY_HEADING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_HEALTH">
            <summary>
            Only works for vehicle and peds
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_MAX_HEALTH">
            <summary>
            Currently it only works with peds.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_MODEL">
            <summary>
            GET_ENTITY_MODEL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_POPULATION_TYPE">
            <summary>
            This native gets an entity's population type.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_ROTATION">
            <summary>
            GET_ENTITY_ROTATION
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_ROTATION_VELOCITY">
            <summary>
            GET_ENTITY_ROTATION_VELOCITY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_ROUTING_BUCKET">
            <summary>
            Gets the routing bucket for the specified entity.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_SCRIPT">
            <summary>
            GET_ENTITY_SCRIPT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_SPEED">
            <summary>
            Gets the current speed of the entity in meters per second.
            
            ```
            To convert to MPH: speed * 2.236936
            To convert to KPH: speed * 3.6
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_TYPE">
            <summary>
            Gets the entity type (as an integer), which can be one of the following defined down below:
            
            **The following entities will return type `1`:**
            
            *   Ped
            *   Player
            *   Animal (Red Dead Redemption 2)
            *   Horse (Red Dead Redemption 2)
            
            **The following entities will return type `2`:**
            
            *   Automobile
            *   Bike
            *   Boat
            *   Heli
            *   Plane
            *   Submarine
            *   Trailer
            *   Train
            *   DraftVeh (Red Dead Redemption 2)
            
            **The following entities will return type `3`:**
            
            *   Object
            *   Door
            *   Pickup
            
            Otherwise, a value of `0` will be returned.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_ENTITY_VELOCITY">
            <summary>
            GET_ENTITY_VELOCITY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_GAME_BUILD_NUMBER">
            <summary>
            Returns the internal build number of the current game being executed.
            
            Possible values:
            
            *   FiveM
                *   1604
                *   2060
                *   2189
                *   2372
                *   2545
                *   2612
                *   2699
                *   2802
                *   2944
                *   3095
            *   RedM
                *   1311
                *   1355
                *   1436
                *   1491
            *   LibertyM
                *   43
            *   FXServer
                *   0
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_GAME_NAME">
            <summary>
            Returns the current game being executed.
            
            Possible values:
            
            | Return value | Meaning                        |
            | ------------ | ------------------------------ |
            | `fxserver`   | Server-side code ('Duplicity') |
            | `fivem`      | FiveM for GTA V                |
            | `libertym`   | LibertyM for GTA IV            |
            | `redm`       | RedM for Red Dead Redemption 2 |
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_GAME_TIMER">
            <summary>
            Gets the current game timer in milliseconds.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HASH_KEY">
            <summary>
            This native converts the passed string to a hash.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_MAIN_ROTOR_HEALTH">
            <summary>
            GET_HELI_MAIN_ROTOR_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HELI_TAIL_ROTOR_HEALTH">
            <summary>
            GET_HELI_TAIL_ROTOR_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_HOST_ID">
            <summary>
            GET_HOST_ID
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_INSTANCE_ID">
            <summary>
            GET_INSTANCE_ID
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_INVOKING_RESOURCE">
            <summary>
            GET_INVOKING_RESOURCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_IS_VEHICLE_ENGINE_RUNNING">
            <summary>
            GET_IS_VEHICLE_ENGINE_RUNNING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_IS_VEHICLE_PRIMARY_COLOUR_CUSTOM">
            <summary>
            GET_IS_VEHICLE_PRIMARY_COLOUR_CUSTOM
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_IS_VEHICLE_SECONDARY_COLOUR_CUSTOM">
            <summary>
            GET_IS_VEHICLE_SECONDARY_COLOUR_CUSTOM
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_LANDING_GEAR_STATE">
            <summary>
            See the client-side [GET_LANDING_GEAR_STATE](#\_0x9B0F3DCA3DB0F4CD) native for a description of landing gear states.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_LAST_PED_IN_VEHICLE_SEAT">
            <summary>
            GET_LAST_PED_IN_VEHICLE_SEAT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NUM_PLAYER_IDENTIFIERS">
            <summary>
            GET_NUM_PLAYER_IDENTIFIERS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NUM_PLAYER_INDICES">
            <summary>
            GET_NUM_PLAYER_INDICES
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NUM_PLAYER_TOKENS">
            <summary>
            GET_NUM_PLAYER_TOKENS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NUM_RESOURCE_METADATA">
            <summary>
            Gets the amount of metadata values with the specified key existing in the specified resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/resources/manifest/)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_NUM_RESOURCES">
            <summary>
            GET_NUM_RESOURCES
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PASSWORD_HASH">
            <summary>
            GET_PASSWORD_HASH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_ARMOUR">
            <summary>
            GET_PED_ARMOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_CAUSE_OF_DEATH">
            <summary>
            GET_PED_CAUSE_OF_DEATH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_DESIRED_HEADING">
            <summary>
            GET_PED_DESIRED_HEADING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_IN_VEHICLE_SEAT">
            <summary>
            GET_PED_IN_VEHICLE_SEAT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_MAX_HEALTH">
            <summary>
            GET_PED_MAX_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_SCRIPT_TASK_COMMAND">
            <summary>
            Gets the script task command currently assigned to the ped.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_SCRIPT_TASK_STAGE">
            <summary>
            Gets the stage of the peds scripted task.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_SOURCE_OF_DAMAGE">
            <summary>
            Get the last entity that damaged the ped. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_SOURCE_OF_DEATH">
            <summary>
            Get the entity that killed the ped. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_SPECIFIC_TASK_TYPE">
            <summary>
            Gets the type of a ped's specific task given an index of the CPedTaskSpecificDataNode nodes.
            A ped will typically have a task at index 0, if a ped has multiple tasks at once they will be in the order 0, 1, 2, etc.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PED_STEALTH_MOVEMENT">
            <summary>
            GET_PED_STEALTH_MOVEMENT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_CAMERA_ROTATION">
            <summary>
            Gets the current camera rotation for a specified player. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_ENDPOINT">
            <summary>
            GET_PLAYER_ENDPOINT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_FAKE_WANTED_LEVEL">
            <summary>
            Gets the current fake wanted level for a specified player. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_FROM_INDEX">
            <summary>
            GET_PLAYER_FROM_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_FROM_STATE_BAG_NAME">
            <summary>
            On the server this will return the players source, on the client it will return the player handle.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_GUID">
            <summary>
            GET_PLAYER_GUID
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_IDENTIFIER">
            <summary>
            GET_PLAYER_IDENTIFIER
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_IDENTIFIER_BY_TYPE">
            <summary>
            Get an identifier from a player by the type of the identifier.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_INVINCIBLE">
            <summary>
            GET_PLAYER_INVINCIBLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_LAST_MSG">
            <summary>
            GET_PLAYER_LAST_MSG
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_MAX_ARMOUR">
            <summary>
            GET_PLAYER_MAX_ARMOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_MAX_HEALTH">
            <summary>
            GET_PLAYER_MAX_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER">
            <summary>
            A getter for [SET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER](#\_0x4A3DC7ECCC321032).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_NAME">
            <summary>
            GET_PLAYER_NAME
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_PED">
            <summary>
            Used to get the player's Ped Entity ID when a valid `playerSrc` is passed.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_PING">
            <summary>
            GET_PLAYER_PING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_ROUTING_BUCKET">
            <summary>
            Gets the routing bucket for the specified player.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_TEAM">
            <summary>
            GET_PLAYER_TEAM
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_TIME_IN_PURSUIT">
            <summary>
            Gets the amount of time player has spent evading the cops.
            Counter starts and increments only when cops are chasing the player.
            If the player is evading, the timer will pause.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_TOKEN">
            <summary>
            Gets a player's token. Tokens can be used to enhance banning logic, however are specific to a server.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_WANTED_CENTRE_POSITION">
            <summary>
            Gets the current known coordinates for the specified player from cops perspective. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_WANTED_LEVEL">
            <summary>
            Returns given players wanted level server-side.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_WEAPON_DAMAGE_MODIFIER">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DAMAGE_MODIFIER](#\_0xCE07B9F7817AADA3).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_WEAPON_DEFENSE_MODIFIER">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DEFENSE_MODIFIER](#\_0x2D83BC011CA14A3C).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_PLAYER_WEAPON_DEFENSE_MODIFIER_2">
            <summary>
            A getter for [\_SET_PLAYER_WEAPON_DEFENSE_MODIFIER\_2](#\_0xBCFDE9EDE4CF27DC).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_REGISTERED_COMMANDS">
            <summary>
            Returns all commands that are registered in the command system.
            The data returned adheres to the following layout:
            
            ```
            [
            {
            "name": "cmdlist"
            },
            {
            "name": "command1"
            }
            ]
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_BY_FIND_INDEX">
            <summary>
            GET_RESOURCE_BY_FIND_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_KVP_FLOAT">
            <summary>
            A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_KVP_INT">
            <summary>
            A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_KVP_STRING">
            <summary>
            A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_METADATA">
            <summary>
            Gets the metadata value at a specified key/index from a resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/resources/manifest/)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_PATH">
            <summary>
            Returns the physical on-disk path of the specified resource.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_RESOURCE_STATE">
            <summary>
            Returns the current state of the specified resource.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_SELECTED_PED_WEAPON">
            <summary>
            Returns a hash of selected ped weapon.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_STATE_BAG_VALUE">
            <summary>
            Returns the value of a state bag key.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_TRAIN_CARRIAGE_ENGINE">
            <summary>
            GET_TRAIN_CARRIAGE_ENGINE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_TRAIN_CARRIAGE_INDEX">
            <summary>
            GET_TRAIN_CARRIAGE_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_BODY_HEALTH">
            <summary>
            GET_VEHICLE_BODY_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_COLOURS">
            <summary>
            GET_VEHICLE_COLOURS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_CUSTOM_PRIMARY_COLOUR">
            <summary>
            GET_VEHICLE_CUSTOM_PRIMARY_COLOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_CUSTOM_SECONDARY_COLOUR">
            <summary>
            GET_VEHICLE_CUSTOM_SECONDARY_COLOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_DASHBOARD_COLOUR">
            <summary>
            GET_VEHICLE_DASHBOARD_COLOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_DIRT_LEVEL">
            <summary>
            GET_VEHICLE_DIRT_LEVEL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_DOOR_LOCK_STATUS">
            <summary>
            ```lua
            enum_VehicleLockStatus = {
                None = 0,
                Locked = 2,
                LockedForPlayer = 3,
                StickPlayerInside = 4, -- Doesn't allow players to exit the vehicle with the exit vehicle key.
                CanBeBrokenInto = 7, -- Can be broken into the car. If the glass is broken, the value will be set to 1
                CanBeBrokenIntoPersist = 8, -- Can be broken into persist
                CannotBeTriedToEnter = 10, -- Cannot be tried to enter (Nothing happens when you press the vehicle enter key).
            }
            ```
            
            It should be [noted](https://forum.cfx.re/t/4863241) that while the [client-side command](#\_0x25BC98A59C2EA962) and its
            setter distinguish between states 0 (unset) and 1 (unlocked), the game will synchronize both as state 0, so the server-side
            command will return only '0' if unlocked.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_DOOR_STATUS">
            <summary>
            GET_VEHICLE_DOOR_STATUS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_DOORS_LOCKED_FOR_PLAYER">
            <summary>
            Currently it only works when set to "all players".
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_ENGINE_HEALTH">
            <summary>
            GET_VEHICLE_ENGINE_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_EXTRA_COLOURS">
            <summary>
            GET_VEHICLE_EXTRA_COLOURS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_FLIGHT_NOZZLE_POSITION">
            <summary>
            Gets the flight nozzel position for the specified vehicle. See the client-side [\_GET_VEHICLE_FLIGHT_NOZZLE_POSITION](#\_0xDA62027C8BDB326E) native for usage examples.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_HANDBRAKE">
            <summary>
            GET_VEHICLE_HANDBRAKE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_HEADLIGHTS_COLOUR">
            <summary>
            GET_VEHICLE_HEADLIGHTS_COLOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_HOMING_LOCKON_STATE">
            <summary>
            Gets the lock on state for the specified vehicle. See the client-side [GET_VEHICLE_HOMING_LOCKON_STATE](#\_0xE6B0E8CFC3633BF0) native for a description of lock on states.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_INTERIOR_COLOUR">
            <summary>
            GET_VEHICLE_INTERIOR_COLOUR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_LIGHTS_STATE">
            <summary>
            GET_VEHICLE_LIGHTS_STATE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_LIVERY">
            <summary>
            GET_VEHICLE_LIVERY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_LOCK_ON_TARGET">
            <summary>
            Gets the vehicle that is locked on to for the specified vehicle.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_NUMBER_PLATE_TEXT">
            <summary>
            GET_VEHICLE_NUMBER_PLATE_TEXT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_NUMBER_PLATE_TEXT_INDEX">
            <summary>
            GET_VEHICLE_NUMBER_PLATE_TEXT_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_PED_IS_IN">
            <summary>
            Gets the vehicle the specified Ped is/was in depending on bool value. This native is used server side when using OneSync.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_PETROL_TANK_HEALTH">
            <summary>
            GET_VEHICLE_PETROL_TANK_HEALTH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_RADIO_STATION_INDEX">
            <summary>
            GET_VEHICLE_RADIO_STATION_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_ROOF_LIVERY">
            <summary>
            GET_VEHICLE_ROOF_LIVERY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_STEERING_ANGLE">
            <summary>
            GET_VEHICLE_STEERING_ANGLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_TYPE">
            <summary>
            Returns the type of the passed vehicle.
            
            ### Vehicle types
            
            *   automobile
            *   bike
            *   boat
            *   heli
            *   plane
            *   submarine
            *   trailer
            *   train
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_TYRE_SMOKE_COLOR">
            <summary>
            GET_VEHICLE_TYRE_SMOKE_COLOR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_WHEEL_TYPE">
            <summary>
            GET_VEHICLE_WHEEL_TYPE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GET_VEHICLE_WINDOW_TINT">
            <summary>
            GET_VEHICLE_WINDOW_TINT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GIVE_WEAPON_COMPONENT_TO_PED">
            <summary>
            GIVE_WEAPON_COMPONENT_TO_PED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.GIVE_WEAPON_TO_PED">
            <summary>
            GIVE_WEAPON_TO_PED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.HAS_ENTITY_BEEN_MARKED_AS_NO_LONGER_NEEDED">
            <summary>
            HAS_ENTITY_BEEN_MARKED_AS_NO_LONGER_NEEDED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.HAS_VEHICLE_BEEN_DAMAGED_BY_BULLETS">
            <summary>
            HAS_VEHICLE_BEEN_DAMAGED_BY_BULLETS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.HAS_VEHICLE_BEEN_OWNED_BY_PLAYER">
            <summary>
            HAS_VEHICLE_BEEN_OWNED_BY_PLAYER
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.INVOKE_FUNCTION_REFERENCE">
            <summary>
            INVOKE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_ACE_ALLOWED">
            <summary>
            IS_ACE_ALLOWED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_BOAT_ANCHORED_AND_FROZEN">
            <summary>
            IS_BOAT_ANCHORED_AND_FROZEN
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_BOAT_WRECKED">
            <summary>
            IS_BOAT_WRECKED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_DUPLICITY_VERSION">
            <summary>
            Gets whether or not this is the CitizenFX server.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_ENTITY_POSITION_FROZEN">
            <summary>
            A getter for [FREEZE_ENTITY_POSITION](#\_0x428CA6DBD1094446).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_ENTITY_VISIBLE">
            <summary>
            This native checks if the given entity is visible.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_FLASH_LIGHT_ON">
            <summary>
            IS_FLASH_LIGHT_ON
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PED_A_PLAYER">
            <summary>
            This native checks if the given ped is a player.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PED_HANDCUFFED">
            <summary>
            IS_PED_HANDCUFFED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PED_RAGDOLL">
            <summary>
            IS_PED_RAGDOLL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PED_STRAFING">
            <summary>
            IS_PED_STRAFING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PED_USING_ACTION_MODE">
            <summary>
            IS_PED_USING_ACTION_MODE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_ACE_ALLOWED">
            <summary>
            IS_PLAYER_ACE_ALLOWED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_COMMERCE_INFO_LOADED">
            <summary>
            Requests whether or not the commerce data for the specified player has loaded.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_COMMERCE_INFO_LOADED_EXT">
            <summary>
            Requests whether or not the commerce data for the specified player has loaded from Tebex.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_EVADING_WANTED_LEVEL">
            <summary>
            This will return true if the player is evading wanted level, meaning that the wanted level stars are blink.
            Otherwise will return false.
            
            If the player is not wanted, it simply returns false.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PLAYER_USING_SUPER_JUMP">
            <summary>
            IS_PLAYER_USING_SUPER_JUMP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_PRINCIPAL_ACE_ALLOWED">
            <summary>
            IS_PRINCIPAL_ACE_ALLOWED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_VEHICLE_ENGINE_STARTING">
            <summary>
            IS_VEHICLE_ENGINE_STARTING
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_VEHICLE_EXTRA_TURNED_ON">
            <summary>
            IS_VEHICLE_EXTRA_TURNED_ON
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_VEHICLE_SIREN_ON">
            <summary>
            IS_VEHICLE_SIREN_ON
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_VEHICLE_TYRE_BURST">
            <summary>
            IS_VEHICLE_TYRE_BURST
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.IS_VEHICLE_WINDOW_INTACT">
            <summary>
            See the client-side [IS_VEHICLE_WINDOW_INTACT](https://docs.fivem.net/natives/?\_0x46E571A0E20D01F1) for a window indexes list.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.LOAD_PLAYER_COMMERCE_DATA">
            <summary>
            Requests the commerce data for the specified player, including the owned SKUs. Use `IS_PLAYER_COMMERCE_INFO_LOADED` to check if it has loaded.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.LOAD_PLAYER_COMMERCE_DATA_EXT">
            <summary>
            Requests the commerce data from Tebex for the specified player, including the owned SKUs. Use `IS_PLAYER_COMMERCE_INFO_LOADED` to check if it has loaded.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.LOAD_RESOURCE_FILE">
            <summary>
            Reads the contents of a text file in a specified resource.
            If executed on the client, this file has to be included in `files` in the resource manifest.
            Example: `local data = LoadResourceFile("devtools", "data.json")`
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.MUMBLE_CREATE_CHANNEL">
            <summary>
            Create a permanent voice channel.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.MUMBLE_IS_PLAYER_MUTED">
            <summary>
            Checks if the player is currently muted
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.MUMBLE_SET_PLAYER_MUTED">
            <summary>
            Mutes or unmutes the specified player
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.NETWORK_GET_ENTITY_FROM_NETWORK_ID">
            <summary>
            NETWORK_GET_ENTITY_FROM_NETWORK_ID
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.NETWORK_GET_ENTITY_OWNER">
            <summary>
            Returns the owner ID of the specified entity.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.NETWORK_GET_FIRST_ENTITY_OWNER">
            <summary>
            Returns the first owner ID of the specified entity.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.NETWORK_GET_NETWORK_ID_FROM_ENTITY">
            <summary>
            NETWORK_GET_NETWORK_ID_FROM_ENTITY
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.NETWORK_GET_VOICE_PROXIMITY_OVERRIDE_FOR_PLAYER">
            <summary>
            NETWORK_GET_VOICE_PROXIMITY_OVERRIDE_FOR_PLAYER
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PERFORM_HTTP_REQUEST_INTERNAL">
            <summary>
            PERFORM_HTTP_REQUEST_INTERNAL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PERFORM_HTTP_REQUEST_INTERNAL_EX">
            <summary>
            PERFORM_HTTP_REQUEST_INTERNAL_EX
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PRINT_STRUCTURED_TRACE">
            <summary>
            Prints 'structured trace' data to the server `file descriptor 3` channel. This is not generally useful outside of
            server monitoring utilities.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PROFILER_ENTER_SCOPE">
            <summary>
            Scope entry for profiler.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PROFILER_EXIT_SCOPE">
            <summary>
            Scope exit for profiler.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.PROFILER_IS_RECORDING">
            <summary>
            Returns true if the profiler is active.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REGISTER_COMMAND">
            <summary>
            Registered commands can be executed by entering them in the client console (this works for client side and server side registered commands). Or by entering them in the server console/through an RCON client (only works for server side registered commands). Or if you use a supported chat resource, like the default one provided in the cfx-server-data repository, then you can enter the command in chat by prefixing it with a `/`.
            
            Commands registered using this function can also be executed by resources, using the [`ExecuteCommand` native](#\_0x561C060B).
            
            The restricted bool is not used on the client side. Permissions can only be checked on the server side, so if you want to limit your command with an ace permission automatically, make it a server command (by registering it in a server script).
            
            **Example result**:
            
            ![](https://i.imgur.com/TaCnG09.png)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REGISTER_CONSOLE_LISTENER">
            <summary>
            Registers a listener for console output messages.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REGISTER_RESOURCE_AS_EVENT_HANDLER">
            <summary>
            An internal function which allows the current resource's HLL script runtimes to receive state for the specified event.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REGISTER_RESOURCE_ASSET">
            <summary>
            **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
            
            Registers a cached resource asset with the resource system, similar to the automatic scanning of the `stream/` folder.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REGISTER_RESOURCE_BUILD_TASK_FACTORY">
            <summary>
            Registers a build task factory for resources.
            The function should return an object (msgpack map) with the following fields:
            
            ```
            {
            // returns whether the specific resource should be built
            shouldBuild = func(resourceName: string): bool,
            
            // asynchronously start building the specific resource.
            // call cb when completed
            build = func(resourceName: string, cb: func(success: bool, status: string): void): void
            }
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_ALL_PED_WEAPONS">
            <summary>
            Parameter `p1` does not seem to be used or referenced in game binaries.\
            **Note:** When called for networked entities, a `CRemoveAllWeaponsEvent` will be created per request.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_BLIP">
            <summary>
            Removes the blip from your map.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_STATE_BAG_CHANGE_HANDLER">
            <summary>
            **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
            
            Removes a handler for changes to a state bag.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_WEAPON_COMPONENT_FROM_PED">
            <summary>
            REMOVE_WEAPON_COMPONENT_FROM_PED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REMOVE_WEAPON_FROM_PED">
            <summary>
            This native removes a specified weapon from your selected ped.  
            Weapon Hashes: pastebin.com/0wwDZgkF  
            Example:  
            C#:  
            Function.Call(Hash.REMOVE_WEAPON_FROM_PED, Game.Player.Character, 0x99B507EA);  
            C++:  
            WEAPON::REMOVE_WEAPON_FROM_PED(PLAYER::PLAYER_PED_ID(), 0x99B507EA);  
            The code above removes the knife from the player.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.REQUEST_PLAYER_COMMERCE_SESSION">
            <summary>
            Requests the specified player to buy the passed SKU. This'll pop up a prompt on the client, which upon acceptance
            will open the browser prompting further purchase details.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SAVE_RESOURCE_FILE">
            <summary>
            Writes the specified data to a file in the specified resource.
            Using a length of `-1` will automatically detect the length assuming the data is a C string.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SCAN_RESOURCE_ROOT">
            <summary>
            Scans the resources in the specified resource root. This function is only available in the 'monitor mode' process and is
            not available for user resources.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SCHEDULE_RESOURCE_TICK">
            <summary>
            Schedules the specified resource to run a tick as soon as possible, bypassing the server's fixed tick rate.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_BLIP_SPRITE">
            <summary>
            Sets the displayed sprite for a specific blip.
            
            There's a [list of sprites](https://docs.fivem.net/game-references/blips/) on the FiveM documentation site.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_CONVAR">
            <summary>
            SET_CONVAR
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_CONVAR_REPLICATED">
            <summary>
            Used to replicate a server variable onto clients.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_CONVAR_SERVER_INFO">
            <summary>
            SET_CONVAR_SERVER_INFO
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_CURRENT_PED_WEAPON">
            <summary>
            SET_CURRENT_PED_WEAPON
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_COORDS">
            <summary>
            Sets the coordinates (world position) for a specified entity, offset by the radius of the entity on the Z axis.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_DISTANCE_CULLING_RADIUS">
            <summary>
            It overrides the default distance culling radius of an entity. Set to `0.0` to reset.
            If you want to interact with an entity outside of your players' scopes set the radius to a huge number.
            
            **WARNING**: Culling natives are deprecated and have known, [unfixable issues](https://forum.cfx.re/t/issue-with-culling-radius-and-server-side-entities/4900677/4)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_HEADING">
            <summary>
            Set the heading of an entity in degrees also known as "Yaw".
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_IGNORE_REQUEST_CONTROL_FILTER">
            <summary>
            It allows to flag an entity to ignore the request control filter policy.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_ROTATION">
            <summary>
            SET_ENTITY_ROTATION
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_ROUTING_BUCKET">
            <summary>
            Sets the routing bucket for the specified entity.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ENTITY_VELOCITY">
            <summary>
            Note that the third parameter(denoted as z) is "up and down" with positive numbers encouraging upwards movement.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_GAME_TYPE">
            <summary>
            SET_GAME_TYPE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_HTTP_HANDLER">
            <summary>
            Sets the handler for HTTP requests made to the executing resource.
            
            Example request URL: `http://localhost:30120/http-test/ping` - this request will be sent to the `http-test` resource with the `/ping` path.
            
            The handler function assumes the following signature:
            
            ```ts
            function HttpHandler(
              request: {
                address: string;
                headers: Record&lt;string, string&gt;;
                method: string;
                path: string;
                setDataHandler(handler: (data: string) =&gt; void): void;
                setDataHandler(handler: (data: ArrayBuffer) =&gt; void, binary: 'binary'): void;
                setCancelHandler(handler: () =&gt; void): void;
              },
              response: {
                writeHead(code: number, headers?: Record&lt;string, string | string[]&gt;): void;
                write(data: string): void;
                send(data?: string): void;
              }
            ): void;
            ```
            
            *   **request**: The request object.
                *   **address**: The IP address of the request sender.
                *   **path**: The path to where the request was sent.
                *   **headers**: The headers sent with the request.
                *   **method**: The request method.
                *   **setDataHandler**: Sets the handler for when a data body is passed with the request. Additionally you can pass the `'binary'` argument to receive a `BufferArray` in JavaScript or `System.Byte[]` in C# (has no effect in Lua).
                *   **setCancelHandler**: Sets the handler for when the request is cancelled.
            *   **response**: An object to control the response.
                *   **writeHead**: Sets the status code &amp; headers of the response. Can be only called once and won't work if called after running other response functions.
                *   **write**: Writes to the response body without sending it. Can be called multiple times.
                *   **send**: Writes to the response body and then sends it along with the status code &amp; headers, finishing the request.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_MAP_NAME">
            <summary>
            SET_MAP_NAME
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_AMMO">
            <summary>
            NativeDB Added Parameter 4: BOOL p3
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_ARMOUR">
            <summary>
            Sets the armor of the specified ped.  
            ped: The Ped to set the armor of.  
            amount: A value between 0 and 100 indicating the value to set the Ped's armor to.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_CAN_RAGDOLL">
            <summary>
            SET_PED_CAN_RAGDOLL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_COMPONENT_VARIATION">
            <summary>
            This native is used to set component variation on a ped. Components, drawables and textures IDs are related to the ped model.
            
            ### MP Freemode list of components
            
            **0**: Face
            **1**: Mask
            **2**: Hair
            **3**: Torso
            **4**: Leg
            **5**: Parachute / bag
            **6**: Shoes
            **7**: Accessory
            **8**: Undershirt
            **9**: Kevlar
            **10**: Badge
            **11**: Torso 2
            
            List of Component IDs
            
            ```cpp
            // Components
            enum ePedVarComp
            {
                PV_COMP_INVALID = 0xFFFFFFFF,
                PV_COMP_HEAD = 0, // "HEAD"
                PV_COMP_BERD = 1, // "BEARD"
                PV_COMP_HAIR = 2, // "HAIR"
                PV_COMP_UPPR = 3, // "UPPER"
                PV_COMP_LOWR = 4, // "LOWER"
                PV_COMP_HAND = 5, // "HAND"
                PV_COMP_FEET = 6, // "FEET"
                PV_COMP_TEEF = 7, // "TEETH"
                PV_COMP_ACCS = 8, // "ACCESSORIES"
                PV_COMP_TASK = 9, // "TASK"
                PV_COMP_DECL = 10, // "DECL"
                PV_COMP_JBIB = 11, // "JBIB"
                PV_COMP_MAX = 12,
            };
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_CONFIG_FLAG">
            <summary>
            cpp
            // Potential names and hash collisions included as comments
            enum ePedConfigFlags {
            	_0x67D1A445 = 0,
            	_0xC63DE95E = 1,
            	CPED_CONFIG_FLAG_NoCriticalHits = 2,
            	CPED_CONFIG_FLAG_DrownsInWater = 3,
            	CPED_CONFIG_FLAG_DisableReticuleFixedLockon = 4,
            	_0x37D196F4 = 5,
            	_0xE2462399 = 6,
            	CPED_CONFIG_FLAG_UpperBodyDamageAnimsOnly = 7,
            	_0xEDDEB838 = 8,
            	_0xB398B6FD = 9,
            	_0xF6664E68 = 10,
            	_0xA05E7CA3 = 11,
            	_0xCE394045 = 12,
            	CPED_CONFIG_FLAG_NeverLeavesGroup = 13,
            	_0xCD8D1411 = 14,
            	_0xB031F1A9 = 15,
            	_0xFE65BEE3 = 16,
            	CPED_CONFIG_FLAG_BlockNonTemporaryEvents = 17,
            	_0x380165BD = 18,
            	_0x07C045C7 = 19,
            	_0x583B5E2D = 20,
            	_0x475EDA58 = 21,
            	_0x8629D05B = 22,
            	_0x1522968B = 23,
            	CPED_CONFIG_FLAG_IgnoreSeenMelee = 24,
            	_0x4CC09C4B = 25,
            	_0x034F3053 = 26,
            	_0xD91BA7CC = 27,
            	_0x5C8DC66E = 28,
            	_0x8902EAA0 = 29,
            	_0x6580B9D2 = 30,
            	_0x0EF7A297 = 31,
            	_0x6BF86E5B = 32,
            	CPED_CONFIG_FLAG_DieWhenRagdoll = 33,
            	CPED_CONFIG_FLAG_HasHelmet = 34,
            	CPED_CONFIG_FLAG_UseHelmet = 35,
            	_0xEEB3D630 = 36,
            	_0xB130D17B = 37,
            	_0x5F071200 = 38,
            	CPED_CONFIG_FLAG_DisableEvasiveDives = 39,
            	_0xC287AAFF = 40,
            	_0x203328CC = 41,
            	CPED_CONFIG_FLAG_DontInfluenceWantedLevel = 42,
            	CPED_CONFIG_FLAG_DisablePlayerLockon = 43,
            	CPED_CONFIG_FLAG_DisableLockonToRandomPeds = 44,
            	_0xEC4A8ACF = 45,
            	_0xDB115BFA = 46,
            	CPED_CONFIG_FLAG_PedBeingDeleted = 47,
            	CPED_CONFIG_FLAG_BlockWeaponSwitching = 48,
            	_0xF8E99565 = 49,
            	_0xDD17FEE6 = 50,
            	_0x7ED9B2C9 = 51,
            	_0x655E8618 = 52,
            	_0x5A6C1F6E = 53,
            	_0xD749FC41 = 54,
            	_0x357F63F3 = 55,
            	_0xC5E60961 = 56,
            	_0x29275C3E = 57,
            	CPED_CONFIG_FLAG_IsFiring = 58,
            	CPED_CONFIG_FLAG_WasFiring = 59,
            	CPED_CONFIG_FLAG_IsStanding = 60,
            	CPED_CONFIG_FLAG_WasStanding = 61,
            	CPED_CONFIG_FLAG_InVehicle = 62,
            	CPED_CONFIG_FLAG_OnMount = 63,
            	CPED_CONFIG_FLAG_AttachedToVehicle = 64,
            	CPED_CONFIG_FLAG_IsSwimming = 65,
            	CPED_CONFIG_FLAG_WasSwimming = 66,
            	CPED_CONFIG_FLAG_IsSkiing = 67,
            	CPED_CONFIG_FLAG_IsSitting = 68,
            	CPED_CONFIG_FLAG_KilledByStealth = 69,
            	CPED_CONFIG_FLAG_KilledByTakedown = 70,
            	CPED_CONFIG_FLAG_Knockedout = 71,
            	_0x3E3C4560 = 72,
            	_0x2994C7B7 = 73,
            	_0x6D59D275 = 74,
            	CPED_CONFIG_FLAG_UsingCoverPoint = 75,
            	CPED_CONFIG_FLAG_IsInTheAir = 76,
            	_0x2D493FB7 = 77,
            	CPED_CONFIG_FLAG_IsAimingGun = 78,
            	_0x14D69875 = 79,
            	_0x40B05311 = 80,
            	_0x8B230BC5 = 81,
            	_0xC74E5842 = 82,
            	_0x9EA86147 = 83,
            	_0x674C746C = 84,
            	_0x3E56A8C2 = 85,
            	_0xC144A1EF = 86,
            	_0x0548512D = 87,
            	_0x31C93909 = 88,
            	_0xA0269315 = 89,
            	_0xD4D59D4D = 90,
            	_0x411D4420 = 91,
            	_0xDF4AEF0D = 92,
            	CPED_CONFIG_FLAG_ForcePedLoadCover = 93,
            	_0x300E4CD3 = 94,
            	_0xF1C5BF04 = 95,
            	_0x89C2EF13 = 96,
            	CPED_CONFIG_FLAG_VaultFromCover = 97,
            	_0x02A852C8 = 98,
            	_0x3D9407F1 = 99,
            	_0x319B4558 = 100,
            	CPED_CONFIG_FLAG_ForcedAim = 101,
            	_0xB942D71A = 102,
            	_0xD26C55A8 = 103,
            	_0xB89E703B = 104,
            	CPED_CONFIG_FLAG_ForceReload = 105,
            	_0xD9E73DA2 = 106,
            	_0xFF71DC2C = 107,
            	_0x1E27E8D8 = 108,
            	_0xF2C53966 = 109,
            	_0xC4DBE247 = 110,
            	_0x83C0A4BF = 111,
            	_0x0E0FAF8C = 112,
            	_0x26616660 = 113,
            	_0x43B80B79 = 114,
            	_0x0D2A9309 = 115,
            	_0x12C1C983 = 116,
            	CPED_CONFIG_FLAG_BumpedByPlayer = 117,
            	_0xE586D504 = 118,
            	_0x52374204 = 119,
            	CPED_CONFIG_FLAG_IsHandCuffed = 120,
            	CPED_CONFIG_FLAG_IsAnkleCuffed = 121,
            	CPED_CONFIG_FLAG_DisableMelee = 122,
            	_0xFE714397 = 123,
            	_0xB3E660BD = 124,
            	_0x5FED6BFD = 125,
            	_0xC9D6F66F = 126,
            	_0x519BC986 = 127,
            	CPED_CONFIG_FLAG_CanBeAgitated = 128,
            	_0x9A4B617C = 129, // CPED_CONFIG_FLAG_FaceDirInsult
            	_0xDAB70E9F = 130,
            	_0xE569438A = 131,
            	_0xBBC77D6D = 132,
            	_0xCB59EF0F = 133,
            	_0x8C5EA971 = 134,
            	CPED_CONFIG_FLAG_IsScuba = 135,
            	CPED_CONFIG_FLAG_WillArrestRatherThanJack = 136,
            	_0xDCE59B58 = 137,
            	CPED_CONFIG_FLAG_RidingTrain = 138,
            	CPED_CONFIG_FLAG_ArrestResult = 139,
            	CPED_CONFIG_FLAG_CanAttackFriendly = 140,
            	_0x98A4BE43 = 141,
            	_0x6901E731 = 142,
            	_0x9EC9BF6C = 143,
            	_0x42841A8F = 144,
            	CPED_CONFIG_FLAG_ShootingAnimFlag = 145,
            	CPED_CONFIG_FLAG_DisableLadderClimbing = 146,
            	CPED_CONFIG_FLAG_StairsDetected = 147,
            	CPED_CONFIG_FLAG_SlopeDetected = 148,
            	_0x1A15670B = 149,
            	_0x61786EE5 = 150,
            	_0xCB9186BD = 151,
            	_0xF0710152 = 152,
            	_0x43DFE310 = 153,
            	_0xC43C624E = 154,
            	CPED_CONFIG_FLAG_CanPerformArrest = 155,
            	CPED_CONFIG_FLAG_CanPerformUncuff = 156,
            	CPED_CONFIG_FLAG_CanBeArrested = 157,
            	_0xF7960FF5 = 158,
            	_0x59564113 = 159,
            	_0x0C6C3099 = 160,
            	_0x645F927A = 161,
            	_0xA86549B9 = 162,
            	_0x8AAF337A = 163,
            	_0x13BAA6E7 = 164,
            	_0x5FB9D1F5 = 165,
            	CPED_CONFIG_FLAG_IsInjured = 166,
            	_0x6398A20B = 167,
            	_0xD8072639 = 168,
            	_0xA05B1845 = 169,
            	_0x83F6D220 = 170,
            	_0xD8430331 = 171,
            	_0x4B547520 = 172,
            	_0xE66E1406 = 173,
            	_0x1C4BFE0C = 174,
            	_0x90008BFA = 175,
            	_0x07C7A910 = 176,
            	_0xF15F8191 = 177,
            	_0xCE4E8BE2 = 178,
            	_0x1D46E4F2 = 179,
            	CPED_CONFIG_FLAG_IsInCustody = 180,
            	_0xE4FD9B3A = 181,
            	_0x67AE0812 = 182,
            	CPED_CONFIG_FLAG_IsAgitated = 183,
            	CPED_CONFIG_FLAG_PreventAutoShuffleToDriversSeat = 184,
            	_0x7B2D325E = 185,
            	CPED_CONFIG_FLAG_EnableWeaponBlocking = 186,
            	CPED_CONFIG_FLAG_HasHurtStarted = 187,
            	CPED_CONFIG_FLAG_DisableHurt = 188,
            	CPED_CONFIG_FLAG_PlayerIsWeird = 189,
            	_0x32FC208B = 190,
            	_0x0C296E5A = 191,
            	_0xE63B73EC = 192,
            	_0x04E9CC80 = 193,
            	CPED_CONFIG_FLAG_UsingScenario = 194,
            	CPED_CONFIG_FLAG_VisibleOnScreen = 195,
            	_0xD88C58A1 = 196,
            	_0x5A3DCF43 = 197, // CPED_CONFIG_FLAG_AvoidUnderSide
            	_0xEA02B420 = 198,
            	_0x3F559CFF = 199,
            	_0x8C55D029 = 200,
            	_0x5E6466F6 = 201,
            	_0xEB5AD706 = 202,
            	_0x0EDDDDE7 = 203,
            	_0xA64F7B1D = 204,
            	_0x48532CBA = 205,
            	_0xAA25A9E7 = 206,
            	_0x415B26B9 = 207,
            	CPED_CONFIG_FLAG_DisableExplosionReactions = 208,
            	CPED_CONFIG_FLAG_DodgedPlayer = 209,
            	_0x67405504 = 210,
            	_0x75DDD68C = 211,
            	_0x2AD879B4 = 212,
            	_0x51486F91 = 213,
            	_0x32F79E21 = 214,
            	_0xBF099213 = 215,
            	_0x054AC8E2 = 216,
            	_0x14E495CC = 217,
            	_0x3C7DF9DF = 218,
            	_0x848FFEF2 = 219,
            	CPED_CONFIG_FLAG_DontEnterLeadersVehicle = 220,
            	_0x2618E1CF = 221,
            	_0x84F722FA = 222,
            	_0xD1B87B1F = 223,
            	_0x728AA918 = 224,
            	CPED_CONFIG_FLAG_DisablePotentialToBeWalkedIntoResponse = 225,
            	CPED_CONFIG_FLAG_DisablePedAvoidance = 226,
            	_0x59E91185 = 227,
            	_0x1EA7225F = 228,
            	CPED_CONFIG_FLAG_DisablePanicInVehicle = 229,
            	_0x6DCA7D88 = 230,
            	_0xFC3E572D = 231,
            	_0x08E9F9CF = 232,
            	_0x2D3BA52D = 233,
            	_0xFD2F53EA = 234,
            	_0x31A1B03B = 235,
            	CPED_CONFIG_FLAG_IsHoldingProp = 236,
            	_0x82ED0A66 = 237, // CPED_CONFIG_FLAG_BlocksPathingWhenDead
            	_0xCE57C9A3 = 238,
            	_0x26149198 = 239,
            	_0x1B33B598 = 240,
            	_0x719B6E87 = 241,
            	_0x13E8E8E8 = 242,
            	_0xF29739AE = 243,
            	_0xABEA8A74 = 244,
            	_0xB60EA2BA = 245,
            	_0x536B0950 = 246,
            	_0x0C754ACA = 247,
            	CPED_CONFIG_FLAG_DisableVehicleSeatRandomAnimations = 248,
            	_0x12659168 = 249,
            	_0x1BDF2F04 = 250,
            	_0x7728FAA3 = 251,
            	_0x6A807ED8 = 252,
            	CPED_CONFIG_FLAG_OnStairs = 253,
            	_0xE1A2F73F = 254,
            	_0x5B3697C8 = 255,
            	_0xF1EB20A9 = 256,
            	_0x8B7DF407 = 257,
            	_0x329DCF1A = 258,
            	_0x8D90DD1B = 259,
            	_0xB8A292B7 = 260,
            	_0x8374B087 = 261,
            	_0x2AF558F0 = 262,
            	_0x82251455 = 263,
            	_0x30CF498B = 264,
            	_0xE1CD50AF = 265,
            	_0x72E4AE48 = 266,
            	_0xC2657EA1 = 267,
            	_0x29FF6030 = 268,
            	_0x8248A5EC = 269,
            	CPED_CONFIG_FLAG_OnStairSlope = 270,
            	_0xA0897933 = 271,
            	CPED_CONFIG_FLAG_DontBlipCop = 272,
            	CPED_CONFIG_FLAG_ClimbedShiftedFence = 273,
            	_0xF7823618 = 274,
            	_0xDC305CCE = 275, // CPED_CONFIG_FLAG_KillWhenTrapped
            	CPED_CONFIG_FLAG_EdgeDetected = 276,
            	_0x92B67896 = 277,
            	_0xCAD677C9 = 278,
            	CPED_CONFIG_FLAG_AvoidTearGas = 279,
            	_0x5276AC7B = 280,
            	_0x1032692A = 281,
            	_0xDA23E7F1 = 282,
            	_0x9139724D = 283,
            	_0xA1457461 = 284,
            	_0x4186E095 = 285,
            	_0xAC68E2EB = 286,
            	CPED_CONFIG_FLAG_RagdollingOnBoat = 287,
            	CPED_CONFIG_FLAG_HasBrandishedWeapon = 288,
            	_0x1B9EE8A1 = 289,
            	_0xF3F5758C = 290,
            	_0x2A9307F1 = 291,
            	_0x7403D216 = 292,
            	_0xA06A3C6C = 293,
            	CPED_CONFIG_FLAG_DisableShockingEvents = 294,
            	_0xF8DA25A5 = 295,
            	_0x7EF55802 = 296,
            	_0xB31F1187 = 297,
            	_0x84315402 = 298,
            	_0x0FD69867 = 299,
            	_0xC7829B67 = 300,
            	CPED_CONFIG_FLAG_DisablePedConstraints = 301,
            	_0x6D23CF25 = 302,
            	_0x2ADA871B = 303,
            	_0x47BC8A58 = 304,
            	_0xEB692FA5 = 305,
            	_0x4A133C50 = 306,
            	_0xC58099C3 = 307,
            	_0xF3D76D41 = 308,
            	_0xB0EEE9F2 = 309,
            	CPED_CONFIG_FLAG_IsInCluster = 310,
            	_0x0FA153EF = 311,
            	_0xD73F5CD3 = 312,
            	_0xD4136C22 = 313,
            	_0xE404CA6B = 314,
            	_0xB9597446 = 315,
            	_0xD5C98277 = 316,
            	_0xD5060A9C = 317,
            	_0x3E5F1CBB = 318,
            	_0xD8BE1D54 = 319,
            	_0x0B1F191F = 320,
            	_0xC995167A = 321,
            	CPED_CONFIG_FLAG_HasHighHeels = 322,
            	_0x86B01E54 = 323,
            	_0x3A56FE15 = 324,
            	_0xC03B736C = 325, // CPED_CONFIG_FLAG_SpawnedAtScenario
            	_0xBBF47729 = 326,
            	_0x22B668A8 = 327,
            	_0x2624D4D4 = 328,
            	CPED_CONFIG_FLAG_DisableTalkTo = 329,
            	CPED_CONFIG_FLAG_DontBlip = 330,
            	CPED_CONFIG_FLAG_IsSwitchingWeapon = 331,
            	_0x630F55F3 = 332,
            	_0x150468FD = 333,
            	_0x914EBD6B = 334,
            	_0x79AF3B6D = 335,
            	_0x75C7A632 = 336,
            	_0x52D530E2 = 337,
            	_0xDB2A90E0 = 338,
            	_0x5922763D = 339,
            	_0x12ADB567 = 340,
            	_0x105C8518 = 341,
            	_0x106F703D = 342,
            	_0xED152C3E = 343,
            	_0xA0EFE6A8 = 344,
            	_0xBF348C82 = 345,
            	_0xCDDFE830 = 346,
            	_0x7B59BD9B = 347,
            	_0x0124C788 = 348,
            	CPED_CONFIG_FLAG_EquipJetpack = 349,
            	_0x08D361A5 = 350,
            	_0xE13D1F7C = 351,
            	_0x40E25FB9 = 352,
            	_0x930629D9 = 353,
            	_0xECCF0C7F = 354,
            	_0xB6E9613B = 355,
            	_0x490C0478 = 356,
            	_0xE8865BEA = 357,
            	_0xF3C34A29 = 358,
            	CPED_CONFIG_FLAG_IsDuckingInVehicle = 359,
            	_0xF660E115 = 360,
            	_0xAB0E6DED = 361,
            	CPED_CONFIG_FLAG_HasReserveParachute = 362,
            	CPED_CONFIG_FLAG_UseReserveParachute = 363,
            	_0x5C5D9CD3 = 364,
            	_0x8F7701F3 = 365,
            	_0xBC4436AD = 366,
            	_0xD7E07D37 = 367,
            	_0x03C4FD24 = 368,
            	_0x7675789A = 369,
            	_0xB7288A88 = 370,
            	_0xC06B6291 = 371,
            	_0x95A4A805 = 372,
            	_0xA8E9A042 = 373,
            	CPED_CONFIG_FLAG_NeverLeaveTrain = 374,
            	_0xBAC674B3 = 375,
            	_0x147F1FFB = 376,
            	_0x4376DD79 = 377,
            	_0xCD3DB518 = 378,
            	_0xFE4BA4B6 = 379,
            	_0x5DF03A55 = 380,
            	_0xBCD816CD = 381,
            	_0xCF02DD69 = 382,
            	_0xF73AFA2E = 383,
            	_0x80B9A9D0 = 384,
            	_0xF601F7EE = 385,
            	_0xA91350FC = 386,
            	_0x3AB23B96 = 387,
            	CPED_CONFIG_FLAG_IsClimbingLadder = 388,
            	CPED_CONFIG_FLAG_HasBareFeet = 389,
            	_0xB4B1CD4C = 390,
            	_0x5459AFB8 = 391,
            	_0x54F27667 = 392,
            	_0xC11D3E8F = 393,
            	_0x5419EB3E = 394,
            	_0x82D8DBB4 = 395,
            	_0x33B02D2F = 396,
            	_0xAE66176D = 397,
            	_0xA2692593 = 398,
            	_0x714C7E31 = 399,
            	_0xEC488AC7 = 400,
            	_0xAE398504 = 401,
            	_0xABC58D72 = 402,
            	_0x5E5B9591 = 403,
            	_0x6BA1091E = 404,
            	_0x77840177 = 405,
            	_0x1C7ACAC4 = 406,
            	_0x124420E9 = 407,
            	_0x75A65587 = 408,
            	_0xDFD2D55B = 409,
            	_0xBDD39919 = 410,
            	_0x43DEC267 = 411,
            	_0xE42B7797 = 412,
            	CPED_CONFIG_FLAG_IsHolsteringWeapon = 413,
            	_0x4F8149F5 = 414,
            	_0xDD9ECA7A = 415,
            	_0x9E7EF9D2 = 416,
            	_0x2C6ED942 = 417,
            	CPED_CONFIG_FLAG_IsSwitchingHelmetVisor = 418,
            	_0xA488727D = 419,
            	_0xCFF5F6DE = 420,
            	_0x6D614599 = 421,
            	CPED_CONFIG_FLAG_DisableVehicleCombat = 422,
            	_0xFE401D26 = 423,
            	CPED_CONFIG_FLAG_FallsLikeAircraft = 424,
            	_0x2B42AE82 = 425,
            	_0x7A95734F = 426,
            	_0xDF4D8617 = 427,
            	_0x578F1F14 = 428,
            	CPED_CONFIG_FLAG_DisableStartEngine = 429,
            	CPED_CONFIG_FLAG_IgnoreBeingOnFire = 430,
            	_0x153C9500 = 431,
            	_0xCB7A632E = 432,
            	_0xDE727981 = 433,
            	CPED_CONFIG_FLAG_DisableHomingMissileLockon = 434,
            	_0x12BBB935 = 435,
            	_0xAD0A1277 = 436,
            	_0xEA6AA46A = 437,
            	CPED_CONFIG_FLAG_DisableHelmetArmor = 438,
            	_0xCB7F3A1E = 439,
            	_0x50178878 = 440,
            	_0x051B4F0D = 441,
            	_0x2FC3DECC = 442,
            	_0xC0030B0B = 443,
            	_0xBBDAF1E9 = 444,
            	_0x944FE59C = 445,
            	_0x506FBA39 = 446,
            	_0xDD45FE84 = 447,
            	_0xE698AE75 = 448,
            	_0x199633F8 = 449,
            	CPED_CONFIG_FLAG_PedIsArresting = 450,
            	CPED_CONFIG_FLAG_IsDecoyPed = 451,
            	_0x3A251D83 = 452,
            	_0xA56F6986 = 453,
            	_0x1D19C622 = 454,
            	_0xB68D3EAB = 455,
            	CPED_CONFIG_FLAG_CanBeIncapacitated = 456,
            	_0x4BD5EBAD = 457,
            }
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_DEFAULT_COMPONENT_VARIATION">
            <summary>
            Sets Ped Default Clothes
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash._SET_PED_EYE_COLOR">
            <summary>
            Used for freemode (online) characters.
            
            Indices:
            
            1.  black
            2.  very light blue/green
            3.  dark blue
            4.  brown
            5.  darker brown
            6.  light brown
            7.  blue
            8.  light blue
            9.  pink
            10. yellow
            11. purple
            12. black
            13. dark green
            14. light brown
            15. yellow/black pattern
            16. light colored spiral pattern
            17. shiny red
            18. shiny half blue/half red
            19. half black/half light blue
            20. white/red perimter
            21. green snake
            22. red snake
            23. dark blue snake
            24. dark yellow
            25. bright yellow
            26. all black
            27. red small pupil
            28. devil blue/black
            29. white small pupil
            30. glossed over
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash._SET_PED_FACE_FEATURE">
            <summary>
            Sets the various freemode face features, e.g. nose length, chin shape.
            
            **Indexes (From 0 to 19):**
            
            Parentheses indicate morph scale/direction as in (-1.0 to 1.0)
            
            *   **0**: Nose Width (Thin/Wide)
            *   **1**: Nose Peak (Up/Down)
            *   **2**: Nose Length (Long/Short)
            *   **3**: Nose Bone Curveness (Crooked/Curved)
            *   **4**: Nose Tip (Up/Down)
            *   **5**: Nose Bone Twist (Left/Right)
            *   **6**: Eyebrow (Up/Down)
            *   **7**: Eyebrow (In/Out)
            *   **8**: Cheek Bones (Up/Down)
            *   **9**: Cheek Sideways Bone Size (In/Out)
            *   **10**: Cheek Bones Width (Puffed/Gaunt)
            *   **11**: Eye Opening (Both) (Wide/Squinted)
            *   **12**: Lip Thickness (Both) (Fat/Thin)
            *   **13**: Jaw Bone Width (Narrow/Wide)
            *   **14**: Jaw Bone Shape (Round/Square)
            *   **15**: Chin Bone (Up/Down)
            *   **16**: Chin Bone Length (In/Out or Backward/Forward)
            *   **17**: Chin Bone Shape (Pointed/Square)
            *   **18**: Chin Hole (Chin Bum)
            *   **19**: Neck Thickness (Thin/Thick)
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash._SET_PED_HAIR_COLOR">
            <summary>
            Used for freemode (online) characters.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_HEAD_BLEND_DATA">
            <summary>
            For more info please refer to [this](https://gtaforums.com/topic/858970-all-gtao-face-ids-pedset-ped-head-blend-data-explained) topic.
            
            **Other information:**
            
            IDs start at zero and go Male Non-DLC, Female Non-DLC, Male DLC, and Female DLC.&lt;/br&gt;
            
            This native function is often called prior to calling natives such as:
            
            *   [`SetPedHairColor`](#\_0xBB43F090)
            *   [`SetPedHeadOverlayColor`](#\_0x78935A27)
            *   [`SetPedHeadOverlay`](#\_0xD28DBA90)
            *   [`SetPedFaceFeature`](#\_0x6C8D4458)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_HEAD_OVERLAY">
            <summary>
            ```
            OverlayID ranges from 0 to 12, index from 0 to _GET_NUM_OVERLAY_VALUES(overlayID)-1, and opacity from 0.0 to 1.0.   
            overlayID       Part                  Index, to disable  
            0               Blemishes             0 - 23, 255  
            1               Facial Hair           0 - 28, 255  
            2               Eyebrows              0 - 33, 255  
            3               Ageing                0 - 14, 255  
            4               Makeup                0 - 74, 255  
            5               Blush                 0 - 6, 255  
            6               Complexion            0 - 11, 255  
            7               Sun Damage            0 - 10, 255  
            8               Lipstick              0 - 9, 255  
            9               Moles/Freckles        0 - 17, 255  
            10              Chest Hair            0 - 16, 255  
            11              Body Blemishes        0 - 11, 255  
            12              Add Body Blemishes    0 - 1, 255  
            ```
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash._SET_PED_HEAD_OVERLAY_COLOR">
            <summary>
            ```
            Used for freemode (online) characters. 
            Called after SET_PED_HEAD_OVERLAY().  
            ```
            
            **Note:**
            
            You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_INTO_VEHICLE">
            <summary>
            SET_PED_INTO_VEHICLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_PROP_INDEX">
            <summary>
            This native is used to set prop variation on a ped. Components, drawables and textures IDs are related to the ped model.
            
            ### MP Freemode list of props
            
            **0**: Hats
            **1**: Glasses
            **2**: Ears
            **6**: Watches
            **7**: Bracelets
            
            List of Prop IDs
            
            ```cpp
            // Props
            enum eAnchorPoints
            {
                ANCHOR_HEAD = 0, // "p_head"
                ANCHOR_EYES = 1, // "p_eyes"
                ANCHOR_EARS = 2, // "p_ears"
                ANCHOR_MOUTH = 3, // "p_mouth"
                ANCHOR_LEFT_HAND = 4, // "p_lhand"
                ANCHOR_RIGHT_HAND = 5, // "p_rhand"
                ANCHOR_LEFT_WRIST = 6, // "p_lwrist"
                ANCHOR_RIGHT_WRIST = 7, // "p_rwrist"
                ANCHOR_HIP = 8, // "p_lhip"
                ANCHOR_LEFT_FOOT = 9, // "p_lfoot"
                ANCHOR_RIGHT_FOOT = 10, // "p_rfoot"
                ANCHOR_PH_L_HAND = 11, // "ph_lhand"
                ANCHOR_PH_R_HAND = 12, // "ph_rhand"
                NUM_ANCHORS = 13,
            };
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_RANDOM_COMPONENT_VARIATION">
            <summary>
            p1 is always 0 in R* scripts; and a quick disassembly seems to indicate that p1 is unused.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_RANDOM_PROPS">
            <summary>
            SET_PED_RANDOM_PROPS
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_RESET_FLAG">
            <summary>
            PED::SET_PED_RESET_FLAG(PLAYER::PLAYER_PED_ID(), 240, 1);
            Known values:
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_TO_RAGDOLL">
            <summary>
            p4/p5: Unusued in TU27
            
            ### Ragdoll Types
            
            **0**: CTaskNMRelax
            **1**: CTaskNMScriptControl: Hardcoded not to work in networked environments.
            **Else**: CTaskNMBalance
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PED_TO_RAGDOLL_WITH_FALL">
            <summary>
            Return variable is never used in R*'s scripts.  
            Not sure what p2 does. It seems like it would be a time judging by it's usage in R*'s scripts, but didn't seem to affect anything in my testings.  
            x, y, and z are coordinates, most likely to where the ped will fall.  
            p7 is probably the force of the fall, but untested, so I left the variable name the same.  
            p8 to p13 are always 0f in R*'s scripts.  
            (Simplified) Example of the usage of the function from R*'s scripts:  
            ped::set_ped_to_ragdoll_with_fall(ped, 1500, 2000, 1, -entity::get_entity_forward_vector(ped), 1f, 0f, 0f, 0f, 0f, 0f, 0f);
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_CONTROL">
            <summary>
            Flags:
            SPC_AMBIENT_SCRIPT = (1 &lt;&lt; 1),
            SPC_CLEAR_TASKS = (1 &lt;&lt; 2),
            SPC_REMOVE_FIRES = (1 &lt;&lt; 3),
            SPC_REMOVE_EXPLOSIONS = (1 &lt;&lt; 4),
            SPC_REMOVE_PROJECTILES = (1 &lt;&lt; 5),
            SPC_DEACTIVATE_GADGETS = (1 &lt;&lt; 6),
            SPC_REENABLE_CONTROL_ON_DEATH = (1 &lt;&lt; 7),
            SPC_LEAVE_CAMERA_CONTROL_ON = (1 &lt;&lt; 8),
            SPC_ALLOW_PLAYER_DAMAGE = (1 &lt;&lt; 9),
            SPC_DONT_STOP_OTHER_CARS_AROUND_PLAYER = (1 &lt;&lt; 10),
            SPC_PREVENT_EVERYBODY_BACKOFF = (1 &lt;&lt; 11),
            SPC_ALLOW_PAD_SHAKE = (1 &lt;&lt; 12)
            See: https://alloc8or.re/gta5/doc/enums/eSetPlayerControlFlag.txt
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_CULLING_RADIUS">
            <summary>
            Sets the culling radius for the specified player.
            Set to `0.0` to reset.
            
            **WARNING**: Culling natives are deprecated and have known, [unfixable issues](https://forum.cfx.re/t/issue-with-culling-radius-and-server-side-entities/4900677/4)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_INVINCIBLE">
            <summary>
            Simply sets you as invincible (Health will not deplete).  
            Use 0x733A643B5B0C53C1 instead if you want Ragdoll enabled, which is equal to:  
            *(DWORD *)(playerPedAddress + 0x188) |= (1 &lt;&lt; 9);
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_MODEL">
            <summary>
            Set the model for a specific Player. Note that this will destroy the current Ped for the Player and create a new one, any reference to the old ped will be invalid after calling this.
            
            As per usual, make sure to request the model first and wait until it has loaded.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_ROUTING_BUCKET">
            <summary>
            Sets the routing bucket for the specified player.
            
            Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_PLAYER_WANTED_LEVEL">
            <summary>
            SET_PLAYER_WANTED_LEVEL
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP">
            <summary>
            A setter for [GET_RESOURCE_KVP_STRING](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP_FLOAT">
            <summary>
            A setter for [GET_RESOURCE_KVP_FLOAT](#\_0x35BDCEEA).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP_FLOAT_NO_SYNC">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP_INT">
            <summary>
            A setter for [GET_RESOURCE_KVP_INT](#\_0x557B586A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP_INT_NO_SYNC">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_RESOURCE_KVP_NO_SYNC">
            <summary>
            Nonsynchronous [SET_RESOURCE_KVP](#\_0x21C7A35B) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ROUTING_BUCKET_ENTITY_LOCKDOWN_MODE">
            <summary>
            Sets the entity lockdown mode for a specific routing bucket.
            
            Lockdown modes are:
            
            | Mode       | Meaning                                                    |
            | ---------- | ---------------------------------------------------------- |
            | `strict`   | No entities can be created by clients at all.              |
            | `relaxed`  | Only script-owned entities created by clients are blocked. |
            | `inactive` | Clients can create any entity they want.                   |
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_ROUTING_BUCKET_POPULATION_ENABLED">
            <summary>
            Sets whether or not the specified routing bucket has automatically-created population enabled.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_STATE_BAG_VALUE">
            <summary>
            Internal function for setting a state bag value.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_ALARM">
            <summary>
            SET_VEHICLE_ALARM
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_BODY_HEALTH">
            <summary>
            p2 often set to 1000.0 in the decompiled scripts.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_COLOUR_COMBINATION">
            <summary>
            Sets the selected vehicle's colors to their default value (specific variant specified using the colorCombination parameter).
            
            Range of possible values for colorCombination is currently unknown, I couldn't find where these values are stored either (Disquse's guess was vehicles.meta but I haven't seen it in there.)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_COLOURS">
            <summary>
            colorPrimary &amp; colorSecondary are the paint indexes for the vehicle.
            
            For a list of valid paint indexes, view: pastebin.com/pwHci0xK
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_CUSTOM_PRIMARY_COLOUR">
            <summary>
            p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_CUSTOM_SECONDARY_COLOUR">
            <summary>
            p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_DIRT_LEVEL">
            <summary>
            Sets the dirt level of the passed vehicle.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_DOOR_BROKEN">
            <summary>
            See eDoorId declared in [`SET_VEHICLE_DOOR_SHUT`](#\_0x93D9BD300D7789E5)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_DOORS_LOCKED">
            <summary>
            // Source GTA VC miss2 leak, matching constants for 0/2/4, testing
            // They use 10 in am_mp_property_int, don't know what it does atm.
            enum eCarLock {
                CARLOCK_NONE = 0,
                CARLOCK_UNLOCKED = 1,
                CARLOCK_LOCKED = 2,
                CARLOCK_LOCKOUT_PLAYER_ONLY = 3,
                CARLOCK_LOCKED_PLAYER_INSIDE = 4,
                CARLOCK_LOCKED_INITIALLY = 5,
                CARLOCK_FORCE_SHUT_DOORS = 6,
                CARLOCK_LOCKED_BUT_CAN_BE_DAMAGED = 7
            };
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.SET_VEHICLE_NUMBER_PLATE_TEXT">
            <summary>
            SET_VEHICLE_NUMBER_PLATE_TEXT
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.START_FIND_KVP">
            <summary>
            START_FIND_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.START_RESOURCE">
            <summary>
            START_RESOURCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.STOP_RESOURCE">
            <summary>
            STOP_RESOURCE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_COMBAT_PED">
            <summary>
            Makes the specified ped attack the target ped.  
            p2 should be 0  
            p3 should be 16
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_DRIVE_BY">
            <summary>
            Example:
            TASK::TASK_DRIVE_BY(l_467[1/*22*/], PLAYER::PLAYER_PED_ID(), 0, 0.0, 0.0, 2.0, 300.0, 100, 0, ${firing_pattern_burst_fire_driveby});
            Needs working example. Doesn't seem to do anything.
            I marked p2 as targetVehicle as all these shooting related tasks seem to have that in common.
            I marked p6 as distanceToShoot as if you think of GTA's Logic with the native SET_VEHICLE_SHOOT natives, it won't shoot till it gets within a certain distance of the target.
            I marked p7 as pedAccuracy as it seems it's mostly 100 (Completely Accurate), 75, 90, etc. Although this could be the ammo count within the gun, but I highly doubt it. I will change this comment once I find out if it's ammo count or not.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_ENTER_VEHICLE">
            <summary>
            speed 1.0 = walk, 2.0 = run  
            p5 1 = normal, 3 = teleport to vehicle, 8 = normal/carjack ped from seat, 16 = teleport directly into vehicle  
            p6 is always 0
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_EVERYONE_LEAVE_VEHICLE">
            <summary>
            TASK_EVERYONE_LEAVE_VEHICLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_GO_STRAIGHT_TO_COORD">
            <summary>
            TASK_GO_STRAIGHT_TO_COORD
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_GO_TO_COORD_ANY_MEANS">
            <summary>
            example from fm_mission_controller
            TASK::TASK_GO_TO_COORD_ANY_MEANS(l_649, sub_f7e86(-1, 0), 1.0, 0, 0, 786603, 0xbf800000);
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_GO_TO_ENTITY">
            <summary>
            The entity will move towards the target until time is over (duration) or get in target's range (distance). p5 and p6 are unknown, but you could leave p5 = 1073741824 or 100 or even 0 (didn't see any difference but on the decompiled scripts, they use 1073741824 mostly) and p6 = 0
            Note: I've only tested it on entity -&gt; ped and target -&gt; vehicle. It could work differently on other entities, didn't try it yet.
            Example: TASK::TASK_GO_TO_ENTITY(pedHandle, vehicleHandle, 5000, 4.0, 100, 1073741824, 0)
            Ped will run towards the vehicle for 5 seconds and stop when time is over or when he gets 4 meters(?) around the vehicle (with duration = -1, the task duration will be ignored).
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_HANDS_UP">
            <summary>
            In the scripts, p3 was always -1.  
            p3 seems to be duration or timeout of turn animation.  
            Also facingPed can be 0 or -1 so ped will just raise hands up.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_LEAVE_ANY_VEHICLE">
            <summary>
            Flags are the same flags used in [`TASK_LEAVE_VEHICLE`](#\_0xD3DBCE61A490BE02)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_LEAVE_VEHICLE">
            <summary>
            Flags from decompiled scripts:  
            0 = normal exit and closes door.  
            1 = normal exit and closes door.  
            16 = teleports outside, door kept closed.  (This flag does not seem to work for the front seats in buses, NPCs continue to exit normally)
            64 = normal exit and closes door, maybe a bit slower animation than 0.  
            256 = normal exit but does not close the door.  
            4160 = ped is throwing himself out, even when the vehicle is still.  
            262144 = ped moves to passenger seat first, then exits normally  
            Others to be tried out: 320, 512, 131072.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_PLAY_ANIM">
            <summary>
            [Animations list](https://alexguirre.github.io/animations-list/)
            
            ```
            float blendInSpeed &gt; normal speed is 8.0f
            ----------------------  
            float blendOutSpeed &gt; normal speed is 8.0f
            ----------------------  
            int duration: time in millisecond  
            ----------------------  
            -1 _ _ _ _ _ _ _&gt; Default (see flag)  
            0 _ _ _ _ _ _ _ &gt; Not play at all  
            Small value _ _ &gt; Slow down animation speed  
            Other _ _ _ _ _ &gt; freeze player control until specific time (ms) has   
            _ _ _ _ _ _ _ _ _ passed. (No effect if flag is set to be   
            _ _ _ _ _ _ _ _ _ controllable.)  
            int flag:  
            ----------------------  
            enum eAnimationFlags  
            {  
             ANIM_FLAG_NORMAL = 0,  
               ANIM_FLAG_REPEAT = 1,  
               ANIM_FLAG_STOP_LAST_FRAME = 2,  
               ANIM_FLAG_UPPERBODY = 16,  
               ANIM_FLAG_ENABLE_PLAYER_CONTROL = 32,  
               ANIM_FLAG_CANCELABLE = 120,  
            };  
            Odd number : loop infinitely  
            Even number : Freeze at last frame  
            Multiple of 4: Freeze at last frame but controllable  
            01 to 15 &gt; Full body  
            10 to 31 &gt; Upper body  
            32 to 47 &gt; Full body &gt; Controllable  
            48 to 63 &gt; Upper body &gt; Controllable  
            ...  
            001 to 255 &gt; Normal  
            256 to 511 &gt; Garbled  
            ...  
            playbackRate:  
            values are between 0.0 and 1.0  
            lockX:    
            0 in most cases 1 for rcmepsilonism8 and rcmpaparazzo_3  
            &gt; 1 for mini@sprunk  
            lockY:  
            0 in most cases   
            1 for missfam5_yoga, missfra1mcs_2_crew_react  
            lockZ:   
                0 for single player   
                Can be 1 but only for MP  
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_PLAY_ANIM_ADVANCED">
            <summary>
            It's similar to the one above, except the first 6 floats let you specify the initial position and rotation of the task. (Ped gets teleported to the position).
            
            [Animations list](https://alexguirre.github.io/animations-list/)
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_REACT_AND_FLEE_PED">
            <summary>
            TASK_REACT_AND_FLEE_PED
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_SHOOT_AT_COORD">
            <summary>
            Firing Pattern Hash Information: https://pastebin.com/Px036isB
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_SHOOT_AT_ENTITY">
            <summary>
            //this part of the code is to determine at which entity the player is aiming, for example if you want to create a mod where you give orders to peds
            Entity aimedentity;
            Player player = PLAYER::PLAYER_ID();
            PLAYER::_GET_AIMED_ENTITY(player, &amp;aimedentity);
            //bg is an array of peds
            TASK::TASK_SHOOT_AT_ENTITY(bg[i], aimedentity, 5000, MISC::GET_HASH_KEY("FIRING_PATTERN_FULL_AUTO"));
            in practical usage, getting the entity the player is aiming at and then task the peds to shoot at the entity, at a button press event would be better.
            Firing Pattern Hash Information: https://pastebin.com/Px036isB
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TASK_WARP_PED_INTO_VEHICLE">
            <summary>
            TASK_WARP_PED_INTO_VEHICLE
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TEMP_BAN_PLAYER">
            <summary>
            TEMP_BAN_PLAYER
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TRIGGER_CLIENT_EVENT_INTERNAL">
            <summary>
            The backing function for TriggerClientEvent.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TRIGGER_EVENT_INTERNAL">
            <summary>
            The backing function for TriggerEvent.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.TRIGGER_LATENT_CLIENT_EVENT_INTERNAL">
            <summary>
            The backing function for TriggerLatentClientEvent.
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.VERIFY_PASSWORD_HASH">
            <summary>
            VERIFY_PASSWORD_HASH
            </summary>
        </member>
        <member name="F:CitizenFX.Server.Native.Hash.WAS_EVENT_CANCELED">
            <summary>
            Returns whether or not the currently executing event was canceled.
            </summary>
        </member>
        <member name="P:CitizenFX.Server.Entity.Position">
            <summary>
            Gets or sets the position of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
            <value>
            The position in world space.
            </value>
        </member>
        <member name="P:CitizenFX.Server.Entity.Rotation">
            <summary>
            Gets or sets the rotation of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
            <value>
            The yaw, pitch, roll rotation values.
            </value>
        </member>
        <member name="P:CitizenFX.Server.Entity.Heading">
            <summary>
            Gets or sets the heading of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
            <value>
            The heading in degrees.
            </value>
        </member>
        <member name="P:CitizenFX.Server.Entity.IsPositionFrozen">
            <summary>
            Sets a value indicating whether this <see cref="T:CitizenFX.Server.Entity"/> should be frozen.
            </summary>
            <value>
            <c>true</c> if this <see cref="T:CitizenFX.Server.Entity"/> position should be frozen; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:CitizenFX.Server.Entity.Velocity">
            <summary>
            Gets or sets the velocity of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Server.Entity.RotationVelocity">
            <summary>
            Gets the rotation velocity of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Server.Entity.Model">
            <summary>
            Gets the model of the this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Server.Entity.Owner">
            <summary>
            Gets the network owner of the this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
            <returns>Returns the <see cref="T:CitizenFX.Server.Player"/> of the network owner.
            Returns <c>null</c> if this <see cref="T:CitizenFX.Server.Entity"/> is in an unowned state.</returns>
        </member>
        <member name="P:CitizenFX.Server.Entity.NetworkId">
            <summary>
            Gets the network ID of the this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Server.Entity.Type">
            <summary>
            Gets the type of this <see cref="T:CitizenFX.Server.Entity"/>.
            </summary>
            <returns>Returns 1 if this <see cref="T:CitizenFX.Server.Entity"/> is a Ped.
            Returns 2 if this <see cref="T:CitizenFX.Server.Entity"/> is a Vehicle.
            Returns 3 if this <see cref="T:CitizenFX.Server.Entity"/> is a Prop.</returns>
        </member>
        <member name="P:CitizenFX.Server.Entity.State">
            <summary>
            Gets the <see cref="T:CitizenFX.Core.StateBag"/> of this <see cref="T:CitizenFX.Server.Entity"/>
            </summary>
        </member>
        <member name="M:CitizenFX.Server.Entity.FromHandle(System.Int32)">
            <summary>
            Creates a new instance of an <see cref="T:CitizenFX.Server.Entity"/> from the given handle.
            </summary>
            <param name="handle">The entity handle.</param>
            <returns>Returns a <see cref="T:CitizenFX.Server.Ped"/> if this handle corresponds to a Ped.
            Returns a <see cref="T:CitizenFX.Server.Vehicle"/> if this handle corresponds to a Vehicle.
            Returns a <see cref="T:CitizenFX.Server.Object"/> if this handle corresponds to a Prop.
            Returns <c>null</c> if no <see cref="T:CitizenFX.Server.Entity"/> exists this the specified <paramref name="handle"/></returns>
        </member>
        <member name="M:CitizenFX.Server.Entity.FromNetworkId(System.Int32)">
            <summary>
            Creates a new instance of an <see cref="T:CitizenFX.Server.Entity"/> from the given network ID.
            </summary>
            <param name="networkId">The entity network ID.</param>
            <returns>Returns a <see cref="T:CitizenFX.Server.Ped"/> if this network ID corresponds to a Ped.
            Returns a <see cref="T:CitizenFX.Server.Vehicle"/> if this network ID corresponds to a Vehicle.
            Returns a <see cref="T:CitizenFX.Server.Object"/> if this network ID corresponds to a Prop.
            Returns <c>null</c> if no <see cref="T:CitizenFX.Server.Entity"/> exists this the specified <paramref name="networkId"/></returns>
        </member>
        <member name="M:CitizenFX.Server.Entity.Equals(CitizenFX.Server.Entity)">
            <summary>
            Checks if two <see cref="T:CitizenFX.Server.Entity"/>s refer to the same <see cref="T:CitizenFX.Server.Entity"/>
            </summary>
            <param name="entity">The other <see cref="T:CitizenFX.Server.Entity"/>.</param>
            <returns><c>true</c> if they are the same <see cref="T:CitizenFX.Server.Entity"/>; otherwise, false</returns>
        </member>
        <member name="M:CitizenFX.Server.Ped.FromPlayerHandle(System.String)">
            <summary>
            Creates a new instance of an <see cref="T:CitizenFX.Server.Ped"/> from the given player handle.
            </summary>
            <param name="handle">The players handle.</param>
            <returns>Returns the <see cref="T:CitizenFX.Server.Ped"/> of the player.
            Returns <c>null</c> if no <see cref="T:CitizenFX.Server.Ped"/> exists for the specified player</returns>
        </member>
        <member name="M:CitizenFX.Server.Player.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:CitizenFX.Server.Player.GetHashCode">
            <inheritdoc />
        </member>
        <member name="P:CitizenFX.Server.IdentifierCollection.Item(System.String)">
            <summary>
            Gets the identifier value of a particular type.
            </summary>
            <example>
            string steamId = player.Identifiers["steam"];
            </example>
            <param name="type">The identifier type to return.</param>
            <returns>The identifier value (without prefix), or null if it could not be found.</returns>
        </member>
    </members>
</doc>
