# إعدادات MongoDB للسيرفر السعودي
# MongoDB Configuration for Saudi FiveM Server

# مسار قاعدة البيانات
# Database path
storage:
  dbPath: ./database
  journal:
    enabled: true

# إعدادات الشبكة
# Network settings
net:
  port: 27017
  bindIp: 127.0.0.1

# إعدادات النظام
# System settings
systemLog:
  destination: file
  logAppend: true
  path: ./logs/mongodb.log

# إعدادات الأمان (اختيارية)
# Security settings (optional)
# security:
#   authorization: enabled

# إعدادات التخزين
# Storage settings
storage:
  engine: wiredTiger
  wiredTiger:
    engineConfig:
      cacheSizeGB: 1
      journalCompressor: snappy
      directoryForIndexes: false
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

# إعدادات العمليات
# Operations settings
operationProfiling:
  slowOpThresholdMs: 100
  mode: slowOp
