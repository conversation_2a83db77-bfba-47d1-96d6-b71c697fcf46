# 🇸🇦 السيرفر السعودي - FiveM

## نظرة عامة
سيرفر FiveM سعودي متكامل مع قاعدة بيانات MongoDB، مصمم خصيصاً للمجتمع السعودي مع دعم كامل للغة العربية والثقافة السعودية.

## المميزات الرئيسية

### 🏛️ النظام الأساسي السعودي
- دعم كامل للغة العربية
- أسماء وهويات سعودية أصيلة
- مدن ومناطق سعودية
- العملة الرسمية: الريال السعودي

### 🗃️ قاعدة البيانات
- **MongoDB** كقاعدة بيانات رئيسية
- حفظ تلقائي لبيانات اللاعبين
- نظام سجلات متقدم
- نسخ احتياطي تلقائي

### 👤 نظام الهوية السعودية
- أرقام هوية سعودية صحيحة
- أسماء عربية أصيلة
- مناطق ومدن سعودية
- تواريخ ميلاد واقعية

### 💼 الوظائف السعودية
- **الشرطة السعودية** - رتب عسكرية صحيحة
- **الهلال الأحمر** - خدمات طبية وإسعاف
- **الوظائف الحكومية** - موظفين حكوميين
- **المعلمين والأطباء** - وظائف مهنية
- **الميكانيكيين والسائقين** - وظائف خدمية

### 🚗 نظام المركبات السعودية
- لوحات أرقام سعودية أصيلة
- مركبات شائعة في السوق السعودي
- محطات وقود أرامكو
- ورش صيانة سعودية

### 💰 النظام الاقتصادي
- العملة: الريال السعودي (ر.س)
- بنوك سعودية (الأهلي، الراجحي، الرياض)
- أجهزة صراف آلي
- متاجر ومحلات سعودية
- نظام ضرائب واقعي

### 💬 نظام الدردشة العربية
- دعم كامل للغة العربية
- رموز تعبيرية عربية
- قنوات دردشة متنوعة
- راديو للوظائف الحكومية

## متطلبات التشغيل

### الخادم
- **FiveM Server** (أحدث إصدار)
- **MongoDB** 4.4 أو أحدث
- **Node.js** 16 أو أحدث
- **Windows Server** أو **Linux**

### قاعدة البيانات
```bash
# تثبيت MongoDB
# Windows: تحميل من الموقع الرسمي
# Linux:
sudo apt-get install mongodb

# تشغيل MongoDB
mongod --dbpath /path/to/database
```

## التثبيت والإعداد

### 1. تحضير الملفات
```bash
# نسخ الملفات إلى مجلد السيرفر
git clone https://github.com/saudi-fivem/server.git
cd server
```

### 2. إعداد قاعدة البيانات
```bash
# تثبيت التبعيات
npm install

# إعداد قاعدة البيانات
npm run setup-db
```

### 3. تكوين السيرفر
```bash
# تعديل server.cfg
# إضافة مفاتيح Steam API و CFX.re License
# تعديل رابط قاعدة البيانات MongoDB
```

### 4. تشغيل السيرفر
```bash
# تشغيل السيرفر
npm start

# أو
FXServer.exe +exec server.cfg
```

## الإعدادات المطلوبة

### server.cfg
```cfg
# مفاتيح مطلوبة
set steam_webApiKey "YOUR_STEAM_API_KEY"
set sv_licenseKey "YOUR_CFXRE_LICENSE_KEY"

# قاعدة البيانات
set mongodb_url "mongodb://localhost:27017/saudi_fivem"
set db_name "saudi_fivem"
```

## الموارد المتضمنة

### الموارد الأساسية
- `saudi-core` - النظام الأساسي
- `saudi-database` - قاعدة البيانات
- `saudi-identity` - نظام الهوية

### الموارد الوظيفية
- `saudi-jobs` - نظام الوظائف
- `saudi-vehicles` - نظام المركبات
- `saudi-economy` - النظام الاقتصادي

### موارد التفاعل
- `saudi-chat` - نظام الدردشة
- `saudi-hud` - واجهة المستخدم
- `saudi-spawn` - نقاط الظهور

## الدعم والمساعدة

### المشاكل الشائعة
1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من تشغيل MongoDB
   - تحقق من رابط الاتصال في server.cfg

2. **عدم ظهور الموارد**
   - تأكد من وجود جميع الملفات
   - تحقق من fxmanifest.lua في كل مورد

3. **مشاكل في اللغة العربية**
   - تأكد من دعم UTF-8
   - استخدم خطوط عربية مناسبة

### التواصل
- **Discord**: [رابط الديسكورد]
- **GitHub**: [رابط المشروع]
- **البريد الإلكتروني**: <EMAIL>

## المساهمة في المشروع
نرحب بمساهماتكم في تطوير السيرفر السعودي!

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---
**تم التطوير بـ ❤️ للمجتمع السعودي**
