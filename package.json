{"name": "saudi-fivem-server", "version": "1.0.0", "description": "سيرفر FiveM سعودي مع قاعدة بيانات MongoDB", "main": "server.cfg", "scripts": {"start": "cd /d %~dp0 && FXServer.exe +exec server.cfg", "install-deps": "npm install mongodb mongoose", "setup-db": "node scripts/setup-database.js"}, "keywords": ["fivem", "saudi", "mongodb", "roleplay", "arabic"], "author": "Saudi FiveM Team", "license": "MIT", "dependencies": {"mongodb": "^6.0.0", "mongoose": "^7.5.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "repository": {"type": "git", "url": "https://github.com/saudi-fivem/server"}, "bugs": {"url": "https://github.com/saudi-fivem/server/issues"}, "homepage": "https://github.com/saudi-fivem/server#readme"}