# ========================================
# إعدادات السيرفر السعودي - FiveM
# Saudi FiveM Server Configuration
# ========================================

# معلومات السيرفر الأساسية
sv_hostname "🇸🇦 السيرفر السعودي | Saudi Official Server"
sv_maxclients 64
sv_endpointprivacy true

# معلومات الاتصال
set steam_webApiKey "9DD8679C59EEBBED89D419C3F2087380"
set sv_licenseKey "cfxk_1o11FlmEpzIMLTHooysYv_46Wk7l"

# إعدادات اللغة والمنطقة الزمنية
set locale "ar_SA"
set timezone "Asia/Riyadh"

# إعدادات قاعدة البيانات
set mongodb_url "mongodb://localhost:27017/saudi_fivem"
set db_name "saudi_fivem"

# تحميل الموارد الأساسية
ensure mapmanager
ensure chat
ensure spawnmanager
ensure sessionmanager
ensure basic-gamemode
ensure hardcap

# الموارد السعودية
ensure saudi-core
ensure saudi-database
ensure saudi-identity
ensure saudi-jobs
ensure saudi-vehicles
ensure saudi-economy
ensure saudi-chat
ensure saudi-hud
ensure saudi-spawn

# إعدادات الأمان
set sv_scriptHookAllowed 0
set sv_enforceGameBuild 2699

# رسائل الترحيب
add_ace group.admin command allow
add_ace group.admin command.quit deny
add_principal identifier.steam:YOUR_STEAM_ID group.admin

# إعدادات الشبكة
endpoint_add_tcp "0.0.0.0:30120"
endpoint_add_udp "0.0.0.0:30120"

# رسالة بدء التشغيل
print("🇸🇦 تم تشغيل السيرفر السعودي بنجاح!")
print("Saudi FiveM Server Started Successfully!")
