@echo off
title السيرفر السعودي - FiveM Server
color 0A

echo.
echo ========================================
echo    🇸🇦 السيرفر السعودي - FiveM
echo    Saudi FiveM Server
echo ========================================
echo.

echo [1/4] التحقق من MongoDB...
echo Checking MongoDB...
timeout /t 2 >nul

echo [2/4] تثبيت التبعيات...
echo Installing dependencies...
call npm install
if errorlevel 1 (
    echo ❌ فشل في تثبيت التبعيات
    echo Failed to install dependencies
    pause
    exit /b 1
)

echo [3/4] إعداد قاعدة البيانات...
echo Setting up database...
call node scripts/setup-database.js
if errorlevel 1 (
    echo ❌ فشل في إعداد قاعدة البيانات
    echo Failed to setup database
    pause
    exit /b 1
)

echo [4/4] تشغيل السيرفر...
echo Starting FiveM Server...
echo.
echo ✅ السيرفر جاهز للتشغيل!
echo Server is ready to start!
echo.
echo 📝 تذكر:
echo Remember:
echo - تأكد من إضافة Steam API Key في server.cfg
echo - تأكد من إضافة CFX.re License Key في server.cfg
echo - تأكد من تشغيل MongoDB
echo.
pause

echo بدء تشغيل السيرفر...
echo Starting server...
FXServer.exe +exec server.cfg

pause
