@echo off
title السيرفر السعودي - FiveM Server
color 0A

echo.
echo ========================================
echo    🇸🇦 السيرفر السعودي - FiveM
echo    Saudi FiveM Server
echo ========================================
echo.

echo [1/3] تثبيت التبعيات...
echo Installing dependencies...
call npm install
if errorlevel 1 (
    echo ❌ فشل في تثبيت التبعيات
    echo Failed to install dependencies
    pause
    exit /b 1
)

echo [2/3] إعداد قاعدة البيانات...
echo Setting up database...
call node scripts/setup-database.js
if errorlevel 1 (
    echo ❌ فشل في إعداد قاعدة البيانات
    echo Failed to setup database
    pause
    exit /b 1
)

echo [3/3] تشغيل السيرفر...
echo Starting FiveM Server...
echo.
echo ✅ السيرفر جاهز للتشغيل!
echo Server is ready to start!
echo.
echo 📝 معلومات السيرفر:
echo Server Info:
echo - Steam API Key: مُعد مسبقاً
echo - CFX.re License Key: مُعد مسبقاً
echo - قاعدة البيانات MongoDB Atlas: متصلة ✅
echo.

echo 🚀 بدء تشغيل السيرفر السعودي...
echo Starting Saudi FiveM Server...
echo.

FXServer.exe +exec server.cfg
