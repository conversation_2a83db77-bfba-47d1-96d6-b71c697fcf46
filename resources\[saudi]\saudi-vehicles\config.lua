Config = {}

-- تنسيق اللوحات السعودية
Config.PlateFormat = {
    private = "### #### ###", -- لوحات خاصة
    government = "### #### ###", -- لوحات حكومية
    diplomatic = "### #### ###", -- لوحات دبلوماسية
    transport = "### #### ###"  -- لوحات نقل
}

-- أحرف اللوحات السعودية
Config.PlateLetters = {
    "أ", "ب", "ج", "د", "ر", "س", "ص", "ط", "ع", "ق", "ك", "ل", "م", "ن", "هـ", "و", "ي"
}

-- المركبات السعودية الشائعة
Config.SaudiVehicles = {
    sedans = {
        {model = "sultan", name = "نيسان التيما", price = 85000},
        {model = "primo", name = "تويوتا كامري", price = 95000},
        {model = "fugitive", name = "هيونداي سوناتا", price = 75000},
        {model = "tailgater", name = "أودي A4", price = 120000},
        {model = "schafter2", name = "مرسيدس E-Class", price = 180000}
    },
    
    suvs = {
        {model = "baller", name = "رينج روفر", price = 250000},
        {model = "cavalcade", name = "كاديلاك إسكاليد", price = 200000},
        {model = "granger", name = "شيفروليه تاهو", price = 150000},
        {model = "landstalker", name = "تويوتا لاند كروزر", price = 180000},
        {model = "seminole", name = "جيب جراند شيروكي", price = 140000}
    },
    
    trucks = {
        {model = "sandking", name = "فورد F-150 رابتور", price = 220000},
        {model = "rebel", name = "شيفروليه سيلفرادو", price = 160000},
        {model = "bodhi2", name = "تويوتا هايلكس", price = 120000}
    },
    
    sports = {
        {model = "elegy2", name = "نيسان GT-R", price = 400000},
        {model = "jester", name = "كورفيت C7", price = 350000},
        {model = "massacro", name = "أستون مارتن", price = 500000},
        {model = "zentorno", name = "لامبورغيني", price = 800000}
    },
    
    motorcycles = {
        {model = "bati", name = "ياماها R1", price = 45000},
        {model = "akuma", name = "كاواساكي نينجا", price = 40000},
        {model = "pcj", name = "هارلي ديفيدسون", price = 55000}
    }
}

-- مركبات الوظائف الحكومية
Config.JobVehicles = {
    police = {
        {model = "police", name = "دورية شرطة", livery = 0},
        {model = "police2", name = "سيارة شرطة", livery = 0},
        {model = "policeb", name = "دراجة شرطة", livery = 0},
        {model = "polmav", name = "هليكوبتر شرطة", livery = 0}
    },
    
    ambulance = {
        {model = "ambulance", name = "سيارة إسعاف", livery = 0},
        {model = "lguard", name = "سيارة طوارئ طبية", livery = 0}
    },
    
    government = {
        {model = "washington", name = "سيارة حكومية", livery = 0},
        {model = "schafter3", name = "سيارة VIP", livery = 0}
    }
}

-- مواقع معارض السيارات
Config.VehicleShops = {
    {
        name = "معرض الرياض للسيارات",
        coords = {x = -56.79, y = -1096.58, z = 25.42},
        categories = {"sedans", "suvs", "trucks"}
    },
    {
        name = "معرض السيارات الرياضية",
        coords = {x = -1255.6, y = -361.16, z = 36.91},
        categories = {"sports"}
    },
    {
        name = "معرض الدراجات النارية",
        coords = {x = 1692.62, y = 3759.50, z = 34.70},
        categories = {"motorcycles"}
    }
}

-- مواقع الكراجات
Config.Garages = {
    {
        name = "كراج الرياض المركزي",
        coords = {x = 215.800, y = -810.057, z = 30.727},
        spawn = {x = 229.700, y = -800.1149, z = 30.5722, h = 157.5}
    },
    {
        name = "كراج جدة",
        coords = {x = -475.531, y = -818.654, z = 30.546},
        spawn = {x = -472.531, y = -815.654, z = 30.546, h = 90.0}
    },
    {
        name = "كراج الدمام",
        coords = {x = 275.023, y = -345.540, z = 44.919},
        spawn = {x = 280.023, y = -340.540, z = 44.919, h = 160.0}
    }
}

-- مواقع ورش الصيانة
Config.RepairShops = {
    {
        name = "ورشة الرياض للصيانة",
        coords = {x = -347.52, y = -133.44, z = 39.01},
        price = 500
    },
    {
        name = "ورشة جدة للإصلاح",
        coords = {x = 1174.81, y = 2640.91, z = 37.75},
        price = 500
    }
}

-- محطات الوقود
Config.FuelStations = {
    {name = "أرامكو - الرياض", coords = {x = 49.4187, y = 2778.793, z = 58.043}},
    {name = "أرامكو - جدة", coords = {x = 263.894, y = 2606.463, z = 44.983}},
    {name = "أرامكو - الدمام", coords = {x = 1039.958, y = 2671.134, z = 39.550}}
}

-- إعدادات الوقود
Config.FuelSettings = {
    enabled = true,
    consumptionRate = 0.5, -- معدل استهلاك الوقود
    fuelPrice = 2.5, -- سعر اللتر بالريال
    maxFuel = 100
}
